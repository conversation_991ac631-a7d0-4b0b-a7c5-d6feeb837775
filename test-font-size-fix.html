<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字體大小修復測試</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', <PERSON>l, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-warning { background: #fff3cd; color: #856404; }
        
        /* 字體大小樣式 */
        .font-size-small { font-size: 14px; }
        .font-size-medium { font-size: 16px; }
        .font-size-large { font-size: 18px; }
        
        .font-size-selector {
            margin: 10px 0;
        }
        
        .font-size-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>字體大小修復測試</h1>
        
        <div class="test-section">
            <h3>1. 錯誤處理器狀態檢查</h3>
            <button class="test-button" onclick="checkErrorHandler()">檢查錯誤處理器</button>
            <div id="errorHandlerStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 字體大小功能測試</h3>
            <div class="font-size-selector">
                <label for="fontSizeSelect">字體大小：</label>
                <select id="fontSizeSelect">
                    <option value="small">小</option>
                    <option value="medium" selected>中</option>
                    <option value="large">大</option>
                </select>
            </div>
            <button class="test-button" onclick="testFontSizeChange()">測試字體大小變更</button>
            <div id="fontSizeStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 錯誤處理測試</h3>
            <button class="test-button" onclick="testErrorHandling()">測試錯誤處理</button>
            <button class="test-button" onclick="testLogEvent()">測試事件記錄</button>
            <div id="errorHandlingStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 完整功能測試</h3>
            <button class="test-button" onclick="runFullTest()">執行完整測試</button>
            <div id="fullTestStatus"></div>
        </div>
        
        <div class="log-container" id="logContainer">
            <strong>測試日誌：</strong><br>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="error-handler.js"></script>
    <script src="ui-manager.js"></script>
    
    <script>
        // 日誌函數
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 狀態指示器
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const statusClass = status === 'success' ? 'status-success' : 
                              status === 'error' ? 'status-error' : 'status-warning';
            element.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
        }
        
        // 檢查錯誤處理器
        function checkErrorHandler() {
            log('開始檢查錯誤處理器...', 'info');
            
            try {
                // 檢查錯誤處理器是否存在
                if (typeof window.errorHandler === 'undefined') {
                    log('❌ window.errorHandler 不存在', 'error');
                    setStatus('errorHandlerStatus', 'error', '錯誤處理器未載入');
                    return false;
                }
                
                // 檢查必要的函數
                const requiredFunctions = ['handleError', 'logEvent', 'ErrorTypes', 'ErrorSeverity'];
                let allFunctionsExist = true;
                
                requiredFunctions.forEach(funcName => {
                    if (typeof window.errorHandler[funcName] === 'undefined') {
                        log(`❌ window.errorHandler.${funcName} 不存在`, 'error');
                        allFunctionsExist = false;
                    } else {
                        log(`✅ window.errorHandler.${funcName} 存在`, 'success');
                    }
                });
                
                if (allFunctionsExist) {
                    log('✅ 錯誤處理器檢查通過', 'success');
                    setStatus('errorHandlerStatus', 'success', '錯誤處理器正常');
                    return true;
                } else {
                    log('❌ 錯誤處理器檢查失敗', 'error');
                    setStatus('errorHandlerStatus', 'error', '錯誤處理器不完整');
                    return false;
                }
                
            } catch (error) {
                log(`❌ 檢查錯誤處理器時發生錯誤: ${error.message}`, 'error');
                setStatus('errorHandlerStatus', 'error', '檢查失敗');
                return false;
            }
        }
        
        // 測試字體大小變更
        function testFontSizeChange() {
            log('開始測試字體大小變更...', 'info');
            
            const fontSizeSelect = document.getElementById('fontSizeSelect');
            if (!fontSizeSelect) {
                log('❌ 找不到字體大小選擇器', 'error');
                setStatus('fontSizeStatus', 'error', '選擇器不存在');
                return;
            }
            
            const testSizes = ['small', 'medium', 'large'];
            let testsPassed = 0;
            
            testSizes.forEach((size, index) => {
                setTimeout(() => {
                    log(`測試字體大小: ${size}`, 'info');
                    
                    // 設置值
                    fontSizeSelect.value = size;
                    
                    // 觸發事件
                    const event = new Event('change', { bubbles: true });
                    fontSizeSelect.dispatchEvent(event);
                    
                    // 檢查結果
                    setTimeout(() => {
                        const hasClass = document.body.classList.contains(`font-size-${size}`);
                        const savedValue = localStorage.getItem('classReadingTracker_fontSize');
                        
                        if (hasClass && savedValue === size) {
                            log(`✅ 字體大小 ${size} 測試通過`, 'success');
                            testsPassed++;
                        } else {
                            log(`❌ 字體大小 ${size} 測試失敗 - CSS類別: ${hasClass}, localStorage: ${savedValue}`, 'error');
                        }
                        
                        // 如果是最後一個測試
                        if (index === testSizes.length - 1) {
                            if (testsPassed === testSizes.length) {
                                setStatus('fontSizeStatus', 'success', '所有測試通過');
                            } else {
                                setStatus('fontSizeStatus', 'error', `${testsPassed}/${testSizes.length} 測試通過`);
                            }
                        }
                    }, 100);
                }, index * 500);
            });
        }
        
        // 測試錯誤處理
        function testErrorHandling() {
            log('開始測試錯誤處理...', 'info');
            
            if (!window.errorHandler) {
                log('❌ 錯誤處理器不存在', 'error');
                setStatus('errorHandlingStatus', 'error', '錯誤處理器不存在');
                return;
            }
            
            try {
                // 測試錯誤處理
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UI_ERROR,
                    severity: window.errorHandler.ErrorSeverity.LOW,
                    message: '這是一個測試錯誤',
                    context: { test: true },
                    timestamp: new Date().toISOString()
                });
                
                log('✅ 錯誤處理測試成功', 'success');
                setStatus('errorHandlingStatus', 'success', '錯誤處理正常');
                
            } catch (error) {
                log(`❌ 錯誤處理測試失敗: ${error.message}`, 'error');
                setStatus('errorHandlingStatus', 'error', '錯誤處理失敗');
            }
        }
        
        // 測試事件記錄
        function testLogEvent() {
            log('開始測試事件記錄...', 'info');
            
            if (!window.errorHandler || typeof window.errorHandler.logEvent !== 'function') {
                log('❌ logEvent 函數不存在', 'error');
                return;
            }
            
            try {
                window.errorHandler.logEvent({
                    type: 'test',
                    message: '這是一個測試事件',
                    context: { test: true },
                    timestamp: new Date().toISOString()
                });
                
                log('✅ 事件記錄測試成功', 'success');
                
            } catch (error) {
                log(`❌ 事件記錄測試失敗: ${error.message}`, 'error');
            }
        }
        
        // 執行完整測試
        function runFullTest() {
            log('開始執行完整測試...', 'info');
            
            let testResults = {
                errorHandler: false,
                fontSize: false,
                errorHandling: false
            };
            
            // 測試 1: 錯誤處理器
            testResults.errorHandler = checkErrorHandler();
            
            // 測試 2: 錯誤處理
            setTimeout(() => {
                testErrorHandling();
                testResults.errorHandling = true;
                
                // 測試 3: 字體大小（延遲執行）
                setTimeout(() => {
                    testFontSizeChange();
                    
                    // 最終結果
                    setTimeout(() => {
                        const allPassed = testResults.errorHandler && testResults.errorHandling;
                        if (allPassed) {
                            log('🎉 所有測試完成！修復成功！', 'success');
                            setStatus('fullTestStatus', 'success', '修復成功');
                        } else {
                            log('⚠️ 部分測試失敗，需要進一步檢查', 'warning');
                            setStatus('fullTestStatus', 'warning', '部分測試失敗');
                        }
                    }, 3000);
                }, 1000);
            }, 500);
        }
        
        // 頁面載入完成後自動檢查
        document.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成，開始自動檢查...', 'info');
            setTimeout(() => {
                checkErrorHandler();
            }, 100);
        });
    </script>
</body>
</html>

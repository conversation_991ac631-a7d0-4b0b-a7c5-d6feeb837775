// 主應用程序入口點 - 負責初始化所有模組和協調它們之間的交互

// 應用程序初始化狀態
let appInitialized = false;

// 應用程序初始化函數
function initApp() {
    console.log('=== 開始初始化應用程序 ===');
    
    if (appInitialized) {
        console.log('應用程序已經初始化過了');
        return;
    }

    try {
        // 初始化各個模組
        console.log('初始化數據管理器...');
        if (window.dataManager) {
            window.dataManager.initDataManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler && typeof window.errorHandler.handleError === 'function') {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: '數據管理器未加載',
                    module: 'dataManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('數據管理器未加載');
            }
        }

        console.log('初始化學生管理器...');
        if (window.studentManager) {
            window.studentManager.initStudentManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: '學生管理器未加載',
                    module: 'studentManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('學生管理器未加載');
            }
        }

        console.log('初始化書籍管理器...');
        if (window.bookManager) {
            window.bookManager.initBookManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: '書籍管理器未加載',
                    module: 'bookManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('書籍管理器未加載');
            }
        }

        console.log('初始化UI管理器...');
        if (window.uiManager) {
            window.uiManager.initUIManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: 'UI管理器未加載',
                    module: 'uiManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('UI管理器未加載');
            }
        }

        console.log('初始化模態框管理器...');
        if (window.modalManager) {
            window.modalManager.initModalManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: '模態框管理器未加載',
                    module: 'modalManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('模態框管理器未加載');
            }
        }

        console.log('初始化統計管理器...');
        if (window.statsManager) {
            window.statsManager.initStatsManager();
        } else {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.UNKNOWN,
                    severity: window.errorHandler.ErrorSeverity.CRITICAL,
                    message: '統計管理器未加載',
                    module: 'statsManager',
                    context: { action: 'initApp' },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('統計管理器未加載');
            }
        }

        // 設置鍵盤快捷鍵
        if (window.modalManager) {
            window.modalManager.setupKeyboardShortcuts();
        }

        // 啟動自動儲存
        if (window.modalManager) {
            window.modalManager.startAutoSave();
        }

        // 設置頁面卸載提醒
        if (window.modalManager) {
            window.modalManager.setupBeforeUnload();
        }

        // 初始化匯入匯出功能
        if (window.modalManager) {
            window.modalManager.initImportExport();
        }

        // 渲染初始書籍列表
        if (window.bookManager) {
            window.bookManager.renderBooks();
        }

        // 添加診斷按鈕（僅在開發環境）
        addDiagnosticButton();

        appInitialized = true;
        console.log('=== 應用程序初始化完成 ===');
        
        // 觸發應用程序初始化完成事件
        window.dispatchEvent(new CustomEvent('appInitialized'));
        
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler && typeof window.errorHandler.handleError === 'function') {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UNKNOWN,
                severity: window.errorHandler.ErrorSeverity.CRITICAL,
                message: '應用程序初始化失敗: ' + error.message,
                context: { action: 'initApp', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('應用程序初始化失敗:', error);
        }
        alert('應用程序初始化失敗，請重新整理頁面');
    }
}

// 添加診斷按鈕（僅在開發環境）
function addDiagnosticButton() {
    // 檢查是否在開發環境
    const isDev = window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' || 
                  window.location.search.includes('debug=true');
    
    if (!isDev) return;

    const headerActions = document.querySelector('.header-actions');
    if (!headerActions) return;

    const diagnosticBtn = document.createElement('button');
    diagnosticBtn.className = 'btn btn-secondary';
    diagnosticBtn.innerHTML = '<i class="fas fa-bug"></i> 診斷';
    diagnosticBtn.onclick = runDiagnostics;

    headerActions.appendChild(diagnosticBtn);
}

// 運行診斷
function runDiagnostics() {
    console.log('=== 開始運行診斷 ===');
    
    const diagnostics = {
        timestamp: new Date().toISOString(),
        modules: {},
        domElements: {},
        data: {},
        errors: []
    };

    // 檢查模組
    const modules = ['dataManager', 'studentManager', 'bookManager', 'uiManager', 'modalManager', 'statsManager'];
    modules.forEach(moduleName => {
        const module = window[moduleName];
        diagnostics.modules[moduleName] = {
            loaded: !!module,
            functions: module ? Object.keys(module).filter(key => typeof module[key] === 'function') : []
        };
    });

    // 檢查關鍵DOM元素
    const elementIds = [
        'studentSelect', 'studentId', 'addBookBtn', 'booksContainer', 
        'bookModal', 'goalModal', 'studentsModal', 'bookDetailModal'
    ];
    
    elementIds.forEach(id => {
        const element = document.getElementById(id);
        diagnostics.domElements[id] = {
            exists: !!element,
            visible: element ? element.offsetParent !== null : false
        };
    });

    // 檢查數據
    try {
        const students = window.studentManager ? window.studentManager.getStudents() : [];
        const books = window.dataManager ? window.dataManager.getBooks() : [];
        const userSettings = window.dataManager ? window.dataManager.getUserSettings() : {};
        
        diagnostics.data = {
            studentsCount: students.length,
            booksCount: books.length,
            yearlyGoal: userSettings.yearlyGoal || 270
        };
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler && typeof window.errorHandler.handleError === 'function') {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.DATA_CORRUPTION,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '數據檢查失敗: ' + error.message,
                context: { action: 'runDiagnostics' },
                timestamp: new Date().toISOString()
            });
        } else {
            diagnostics.errors.push(`數據檢查失敗: ${error.message}`);
        }
    }

    // 輸出診斷結果
    console.log('診斷結果:', diagnostics);

    // 在頁面上顯示診斷結果
    const diagnosticDiv = document.createElement('div');
    diagnosticDiv.id = 'app-diagnostics';
    diagnosticDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: #fff;
        border: 2px solid #007bff;
        border-radius: 10px;
        padding: 15px;
        max-width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;

    const existingDiagnostic = document.getElementById('app-diagnostics');
    if (existingDiagnostic) {
        existingDiagnostic.remove();
    }

    let html = '<h4 style="margin: 0 0 10px 0; color: #007bff;">應用程序診斷結果</h4>';
    
    // 模組狀態
    html += '<h5>模組狀態:</h5><ul>';
    Object.entries(diagnostics.modules).forEach(([name, info]) => {
        const status = info.loaded ? '✅' : '❌';
        html += `<li>${status} ${name}: ${info.loaded ? '已加載' : '未加載'}`;
        if (info.loaded && info.functions.length > 0) {
            html += ` (${info.functions.length} 個函數)`;
        }
        html += '</li>';
    });
    html += '</ul>';
    
    // DOM元素狀態
    html += '<h5>DOM元素狀態:</h5><ul>';
    Object.entries(diagnostics.domElements).forEach(([id, info]) => {
        const status = info.exists ? '✅' : '❌';
        const visibility = info.exists && info.visible ? '可見' : '隱藏';
        html += `<li>${status} ${id}: ${info.exists ? '存在' : '不存在'} (${visibility})</li>`;
    });
    html += '</ul>';
    
    // 數據狀態
    html += '<h5>數據狀態:</h5><ul>';
    html += `<li>學生數量: ${diagnostics.data.studentsCount}</li>`;
    html += `<li>書籍數量: ${diagnostics.data.booksCount}</li>`;
    html += `<li>年度目標: ${diagnostics.data.yearlyGoal}</li>`;
    html += '</ul>';
    
    // 錯誤
    if (diagnostics.errors.length > 0) {
        html += '<h5>錯誤:</h5><ul>';
        diagnostics.errors.forEach(error => {
            html += `<li style="color: red;">${error}</li>`;
        });
        html += '</ul>';
    }

    html += `
        <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">關閉</button>
    `;

    diagnosticDiv.innerHTML = html;
    document.body.appendChild(diagnosticDiv);

    console.log('=== 診斷完成 ===');
}

// 應用程序重置函數
function resetApp() {
    if (confirm('確定要重置應用程序嗎？這將清除所有本地數據。')) {
        // 清除本地存儲
        localStorage.clear();
        
        // 重新載入頁面
        window.location.reload();
    }
}

// 應用程序更新函數
function updateApp() {
    console.log('檢查應用程序更新...');
    
    // 這裡可以添加檢查更新的邏輯
    // 例如檢查版本號、下載新版本等
    
    alert('當前已是最新版本');
}

// 導出函數供外部使用
window.app = {
    initApp,
    runDiagnostics,
    resetApp,
    updateApp,
    isInitialized: () => appInitialized
};

// 當DOM載入完成後初始化應用程序
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM載入完成，準備初始化應用程序');
    
    // 等待一小段時間確保所有模組都已載入
    setTimeout(() => {
        initApp();
    }, 100);
});

// 當所有資源載入完成後
window.addEventListener('load', function() {
    console.log('所有資源載入完成');
    
    // 如果應用程序尚未初始化，嘗試再次初始化
    if (!appInitialized) {
        console.log('應用程序尚未初始化，嘗試重新初始化');
        setTimeout(() => {
            initApp();
        }, 500);
    }
});
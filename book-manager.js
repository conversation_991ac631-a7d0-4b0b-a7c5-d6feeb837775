// 書籍管理模組 - 負責書籍相關的所有功能

// 全局變數
let currentFilter = 'all';
let currentSort = 'student';
let currentView = 'grid';
let selectedStudent = '';
let editingBookId = null;

// 初始化書籍管理器
function initBookManager() {
    console.log('初始化書籍管理器...');
    setupEventListeners();
}

// 設置事件監聽器
function setupEventListeners() {
    // 搜尋和篩選
    const searchInput = document.getElementById('searchInput');
    const sortSelect = document.getElementById('sortSelect');
    const studentSelect = document.getElementById('studentSelect');
    
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSort);
    }
    
    if (studentSelect) {
        studentSelect.addEventListener('change', handleStudentFilter);
    }

    // 篩選按鈕
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            // 只對狀態篩選按鈕處理 active 狀態
            if (e.target.dataset.status) {
                document.querySelectorAll('.filter-btn[data-status]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                currentFilter = e.target.dataset.status;
                renderBooks();
            }
        });
    });

    // 學生篩選按鈕
    const showAllStudents = document.getElementById('showAllStudents');
    const showTopReaders = document.getElementById('showTopReaders');
    
    if (showAllStudents) {
        showAllStudents.addEventListener('click', () => {
            selectedStudent = '';
            if (studentSelect) {
                studentSelect.value = '';
            }
            renderBooks();
        });
    }
    
    if (showTopReaders) {
        showTopReaders.addEventListener('click', () => {
            showTopReadersModal();
        });
    }

    // 視圖切換
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            currentView = e.target.dataset.view;
            toggleView();
        });
    });
}

// 處理學生篩選
function handleStudentFilter(e) {
    selectedStudent = e.target.value;
    renderBooks();
}

// 處理搜尋
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    renderBooks(searchTerm);
}

// 處理排序
function handleSort(e) {
    currentSort = e.target.value;
    renderBooks();
}

// 顯示閱讀之星
function showTopReadersModal() {
    const topReaders = window.studentManager ? window.studentManager.getTopReaders(10) : [];
    
    if (topReaders.length === 0) {
        alert('還沒有學生完成閱讀！');
        return;
    }

    let message = '🌟 班級閱讀之星 🌟\n\n';
    topReaders.forEach((student, index) => {
        message += `${index + 1}. ${student.studentName}：${student.completedBooks}本\n`;
    });

    alert(message);
}

// 切換視圖
function toggleView() {
    const booksContainer = document.getElementById('booksContainer');
    if (booksContainer) {
        if (currentView === 'list') {
            booksContainer.classList.add('list-view');
        } else {
            booksContainer.classList.remove('list-view');
        }
    }
}

// 渲染書籍列表
function renderBooks(searchTerm = '') {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    let filteredBooks = books;

    // 篩選學生
    if (selectedStudent) {
        filteredBooks = filteredBooks.filter(book => book.studentId === selectedStudent);
    }

    // 篩選狀態
    if (currentFilter !== 'all') {
        filteredBooks = filteredBooks.filter(book => book.status === currentFilter);
    }

    // 搜尋
    if (searchTerm) {
        filteredBooks = filteredBooks.filter(book => {
            const student = window.studentManager ? window.studentManager.getStudent(book.studentId) : null;
            return book.title.toLowerCase().includes(searchTerm) ||
                   (book.author && book.author.toLowerCase().includes(searchTerm)) ||
                   (student && student.name.toLowerCase().includes(searchTerm)) ||
                   (book.tags && book.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
        });
    }

    // 排序
    filteredBooks.sort((a, b) => {
        switch (currentSort) {
            case 'student':
                return a.studentId.localeCompare(b.studentId);
            case 'title':
                return a.title.localeCompare(b.title);
            case 'author':
                return (a.author || '').localeCompare(b.author || '');
            case 'progress':
                const progressA = a.totalPages > 0 ? (a.currentPage / a.totalPages) : 0;
                const progressB = b.totalPages > 0 ? (b.currentPage / b.totalPages) : 0;
                return progressB - progressA;
            case 'rating':
                return (b.rating || 0) - (a.rating || 0);
            case 'dateAdded':
                return new Date(b.dateAdded) - new Date(a.dateAdded);
            default:
                return 0;
        }
    });

    // 渲染
    const booksContainer = document.getElementById('booksContainer');
    const emptyState = document.getElementById('emptyState');
    
    if (booksContainer && emptyState) {
        if (filteredBooks.length === 0) {
            booksContainer.innerHTML = '';
            emptyState.style.display = 'block';
        } else {
            emptyState.style.display = 'none';
            booksContainer.innerHTML = filteredBooks.map(book => createBookCard(book)).join('');

            // 綁定卡片事件
            bindBookCardEvents();
        }
    }
}

// 創建書籍卡片
function createBookCard(book) {
    const progress = book.totalPages > 0 ? Math.round((book.currentPage / book.totalPages) * 100) : 0;
    const statusClass = `status-${book.status}`;
    const statusText = getStatusText(book.status);
    const student = window.studentManager ? window.studentManager.getStudent(book.studentId) : { id: book.studentId, name: '未知學生' };

    return `
        <div class="book-card" data-book-id="${book.id}">
            <div class="book-header">
                <div class="book-info">
                    <div class="student-name">
                        <span class="student-number">${student.id}</span>
                        ${student.name}
                    </div>
                    <h3>${escapeHtml(book.title)}</h3>
                    ${book.author ? `<div class="author">作者：${escapeHtml(book.author)}</div>` : ''}
                </div>
                <span class="book-status ${statusClass}">${statusText}</span>
            </div>

            ${book.tags && book.tags.length > 0 ? `
                <div class="book-tags">
                    ${book.tags.map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('')}
                </div>
            ` : ''}

            ${book.totalPages > 0 ? `
                <div class="book-progress">
                    <div class="progress-text">
                        <span>進度</span>
                        <span>${book.currentPage} / ${book.totalPages} 頁 (${progress}%)</span>
                    </div>
                    <div class="progress-bar-book">
                        <div class="progress-fill-book" style="width: ${progress}%"></div>
                    </div>
                </div>
            ` : ''}

            <div class="book-meta">
                <div class="book-rating">
                    ${generateStarRating(book.rating || 0)}
                </div>
                <div class="book-actions">
                    <button class="action-btn edit" onclick="window.bookManager.openBookModal('${book.id}')" title="編輯">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="window.bookManager.deleteBook('${book.id}')" title="刪除">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="action-btn" onclick="window.bookManager.showBookDetail('${book.id}')" title="詳情">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 綁定書籍卡片事件
function bindBookCardEvents() {
    document.querySelectorAll('.book-card').forEach(card => {
        card.addEventListener('click', (e) => {
            // 如果點擊的是按鈕，不觸發卡片點擊
            if (e.target.closest('.book-actions')) {
                return;
            }

            const bookId = card.dataset.bookId;
            showBookDetail(bookId);
        });
    });
}

// 生成星級評分
function generateStarRating(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="fas fa-star empty"></i>';
        }
    }
    return stars;
}

// 獲取狀態文字
function getStatusText(status) {
    const statusMap = {
        'to-read': '待讀',
        'reading': '閱讀中',
        'completed': '已完成',
        'abandoned': '暫停'
    };
    return statusMap[status] || '未知';
}

// HTML 轉義
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 開啟書籍模態框
function openBookModal(bookId = null) {
    editingBookId = bookId;
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const book = bookId ? books.find(b => b.id === bookId) : null;

    // 觸發事件打開模態框
    window.dispatchEvent(new CustomEvent('openBookModal', { detail: { book, editingBookId: bookId } }));
}

// 刪除書籍
function deleteBook(bookId) {
    if (confirm('確定要刪除這本書嗎？此操作無法復原。')) {
        if (window.dataManager) {
            window.dataManager.deleteBook(bookId);
            renderBooks();
            
            // 觸發事件通知統計模組更新
            window.dispatchEvent(new CustomEvent('bookDeleted', { detail: { bookId } }));
        }
    }
}

// 顯示書籍詳情
function showBookDetail(bookId) {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const book = books.find(b => b.id === bookId);
    if (!book) return;

    const progress = book.totalPages > 0 ? Math.round((book.currentPage / book.totalPages) * 100) : 0;
    const statusText = getStatusText(book.status);
    const student = window.studentManager ? window.studentManager.getStudent(book.studentId) : { id: book.studentId, name: '未知學生', fullName: '' };

    const detailContent = `
        <div class="detail-header">
            ${book.coverImage ? `<img src="${book.coverImage}" alt="${book.title}" class="detail-cover">` : ''}
            <div class="detail-info">
                <div class="student-info">
                    <span class="student-number">${student.id}</span>
                    <strong>${student.name}</strong>（${student.fullName}）
                </div>
                <h2>${escapeHtml(book.title)}</h2>
                ${book.author ? `<div class="author">作者：${escapeHtml(book.author)}</div>` : ''}
                <div class="book-rating">
                    ${generateStarRating(book.rating || 0)}
                </div>
                ${book.tags && book.tags.length > 0 ? `
                    <div class="book-tags">
                        ${book.tags.map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        </div>

        <div class="detail-meta">
            <div class="meta-item">
                <span class="meta-value">${statusText}</span>
                <span class="meta-label">狀態</span>
            </div>
            ${book.totalPages > 0 ? `
                <div class="meta-item">
                    <span class="meta-value">${progress}%</span>
                    <span class="meta-label">進度</span>
                </div>
                <div class="meta-item">
                    <span class="meta-value">${book.currentPage}</span>
                    <span class="meta-label">目前頁數</span>
                </div>
                <div class="meta-item">
                    <span class="meta-value">${book.totalPages}</span>
                    <span class="meta-label">總頁數</span>
                </div>
            ` : ''}
            <div class="meta-item">
                <span class="meta-value">${book.rating || 0}/5</span>
                <span class="meta-label">評分</span>
            </div>
        </div>

        ${book.notes ? `
            <div class="detail-notes">
                <h3>閱讀筆記</h3>
                <p>${escapeHtml(book.notes).replace(/\n/g, '<br>')}</p>
            </div>
        ` : ''}
    `;

    // 觸發事件顯示書籍詳情模態框
    window.dispatchEvent(new CustomEvent('showBookDetail', { detail: { detailContent } }));
}

// 快速更新進度
function quickUpdateProgress(bookId, increment) {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const book = books.find(b => b.id === bookId);
    if (!book || !book.totalPages) return;

    const newPage = Math.max(0, Math.min(book.totalPages, book.currentPage + increment));
    book.currentPage = newPage;

    // 如果讀完了，自動設為已完成
    if (newPage === book.totalPages && book.status === 'reading') {
        book.status = 'completed';
    }

    book.dateModified = new Date().toISOString();
    
    if (window.dataManager) {
        window.dataManager.updateBook(bookId, book);
        renderBooks();
        
        // 觸發事件通知統計模組更新
        window.dispatchEvent(new CustomEvent('bookUpdated', { detail: { bookId } }));
    }
}

// 批量操作
function bulkOperation(operation, bookIds) {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    
    bookIds.forEach(bookId => {
        const book = books.find(b => b.id === bookId);
        if (!book) return;

        switch (operation) {
            case 'delete':
                if (window.dataManager) {
                    window.dataManager.deleteBook(bookId);
                }
                break;
            case 'mark-completed':
                book.status = 'completed';
                if (book.totalPages > 0) {
                    book.currentPage = book.totalPages;
                }
                if (window.dataManager) {
                    window.dataManager.updateBook(bookId, book);
                }
                break;
            case 'mark-reading':
                book.status = 'reading';
                if (window.dataManager) {
                    window.dataManager.updateBook(bookId, book);
                }
                break;
            case 'mark-to-read':
                book.status = 'to-read';
                book.currentPage = 0;
                if (window.dataManager) {
                    window.dataManager.updateBook(bookId, book);
                }
                break;
        }

        book.dateModified = new Date().toISOString();
    });

    renderBooks();
    
    // 觸發事件通知統計模組更新
    window.dispatchEvent(new CustomEvent('booksBulkUpdated', { detail: { operation, bookIds } }));
}

// 處理書籍表單提交
function handleBookSubmit(e) {
    e.preventDefault();

    const studentId = document.getElementById('studentId').value;
    const bookData = {
        studentId: studentId,
        title: document.getElementById('bookTitle').value.trim(),
        author: document.getElementById('bookAuthor').value.trim(),
        totalPages: parseInt(document.getElementById('totalPages').value) || 0,
        currentPage: parseInt(document.getElementById('currentPage').value) || 0,
        status: document.getElementById('bookStatus').value,
        coverImage: document.getElementById('bookCover').value.trim(),
        tags: document.getElementById('bookTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
        notes: document.getElementById('bookNotes').value.trim(),
        rating: getCurrentRating()
    };

    // 驗證必填欄位
    if (!studentId) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.VALIDATION,
                severity: window.errorHandler.ErrorSeverity.LOW,
                message: '請選擇學生',
                field: 'studentId',
                context: { action: 'handleBookSubmit', bookData },
                timestamp: new Date().toISOString()
            });
        } else {
            alert('請選擇學生');
        }
        return;
    }

    if (!bookData.title) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.VALIDATION,
                severity: window.errorHandler.ErrorSeverity.LOW,
                message: '請輸入書名',
                field: 'bookTitle',
                context: { action: 'handleBookSubmit', bookData },
                timestamp: new Date().toISOString()
            });
        } else {
            alert('請輸入書名');
        }
        return;
    }

    // 驗證頁數
    if (bookData.currentPage > bookData.totalPages && bookData.totalPages > 0) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.VALIDATION,
                severity: window.errorHandler.ErrorSeverity.LOW,
                message: '目前頁數不能超過總頁數',
                field: 'currentPage',
                context: { action: 'handleBookSubmit', bookData, currentPage: bookData.currentPage, totalPages: bookData.totalPages },
                timestamp: new Date().toISOString()
            });
        } else {
            alert('目前頁數不能超過總頁數');
        }
        return;
    }

    if (editingBookId) {
        // 更新現有書籍
        if (window.dataManager) {
            window.dataManager.updateBook(editingBookId, bookData);
        }
    } else {
        // 新增書籍
        if (window.dataManager) {
            window.dataManager.addBook(bookData);
        }
    }

    renderBooks();
    
    // 觸發事件通知統計模組更新
    window.dispatchEvent(new CustomEvent('bookSaved', { detail: { bookId: editingBookId } }));
    
    // 觸發事件關閉模態框
    window.dispatchEvent(new CustomEvent('closeBookModal'));
}

// 獲取當前評分
function getCurrentRating() {
    const ratingInput = document.getElementById('ratingInput');
    if (ratingInput) {
        return ratingInput.querySelectorAll('i.active').length;
    }
    return 0;
}

// 設定評分
function setRating(rating) {
    const ratingInput = document.getElementById('ratingInput');
    if (ratingInput) {
        const stars = ratingInput.querySelectorAll('i');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }
}

// 處理評分點擊
function handleRatingClick(e) {
    if (e.target.tagName === 'I') {
        const rating = parseInt(e.target.dataset.rating);
        setRating(rating);
    }
}

// 導出函數供其他模組使用
window.bookManager = {
    initBookManager,
    renderBooks,
    openBookModal,
    deleteBook,
    showBookDetail,
    quickUpdateProgress,
    bulkOperation,
    handleBookSubmit,
    handleRatingClick,
    setRating,
    getCurrentRating
};

// 批次新增閱讀記錄功能
function openBatchAddModal() {
    // 觸發事件打開批次新增模態框
    window.dispatchEvent(new CustomEvent('openBatchAddModal'));
}

// 處理批次新增文字輸入
function processBatchTextInput() {
    const textInput = document.getElementById('batchTextInput');
    const resultsContent = document.getElementById('resultsContent');
    const batchResults = document.getElementById('batchResults');
    
    if (!textInput || !textInput.value.trim()) {
        alert('請輸入書籍資料');
        return;
    }
    
    const lines = textInput.value.trim().split('\n');
    const results = {
        success: [],
        errors: []
    };
    
    lines.forEach((line, index) => {
        if (!line.trim()) return;
        
        try {
            const parts = line.split(',').map(part => part.trim());
            if (parts.length < 3) {
                throw new Error('資料格式不正確');
            }
            
            const bookData = {
                studentId: parts[0],
                title: parts[1],
                author: parts[2] || '',
                totalPages: parseInt(parts[3]) || 0,
                currentPage: parseInt(parts[4]) || 0,
                status: parts[5] || 'to-read',
                tags: parts[6] ? parts[6].split(';').map(tag => tag.trim()).filter(tag => tag) : []
            };
            
            // 驗證學生ID
            const student = window.studentManager ? window.studentManager.getStudent(bookData.studentId) : null;
            if (!student) {
                throw new Error(`找不到學生ID: ${bookData.studentId}`);
            }
            
            // 驗證狀態
            const validStatuses = ['to-read', 'reading', 'completed', 'abandoned'];
            if (!validStatuses.includes(bookData.status)) {
                throw new Error(`無效的狀態: ${bookData.status}`);
            }
            
            // 驗證頁數
            if (bookData.currentPage > bookData.totalPages && bookData.totalPages > 0) {
                throw new Error('目前頁數不能超過總頁數');
            }
            
            // 新增書籍
            if (window.dataManager) {
                const newBook = window.dataManager.addBook(bookData);
                results.success.push({
                    line: index + 1,
                    book: newBook,
                    message: `成功新增: ${bookData.title}`
                });
            }
            
        } catch (error) {
            results.errors.push({
                line: index + 1,
                message: `第 ${index + 1} 行錯誤: ${error.message}`,
                data: line
            });
        }
    });
    
    // 顯示結果
    displayBatchResults(results);
    
    // 如果有成功的記錄，重新渲染書籍列表
    if (results.success.length > 0) {
        renderBooks();
        
        // 觸發事件通知統計模組更新
        window.dispatchEvent(new CustomEvent('booksBulkUpdated', {
            detail: {
                operation: 'batch-add',
                bookIds: results.success.map(r => r.book.id)
            }
        }));
    }
}

// 處理批次新增CSV檔案上傳
function processBatchCSVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const results = {
                success: [],
                errors: []
            };
            
            // 跳過標題行（如果有）
            const startIndex = lines[0].includes('學生ID') || lines[0].includes('studentId') ? 1 : 0;
            
            for (let i = startIndex; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                try {
                    const parts = parseCSVLine(line);
                    if (parts.length < 3) {
                        throw new Error('資料格式不正確');
                    }
                    
                    const bookData = {
                        studentId: parts[0],
                        title: parts[1],
                        author: parts[2] || '',
                        totalPages: parseInt(parts[3]) || 0,
                        currentPage: parseInt(parts[4]) || 0,
                        status: parts[5] || 'to-read',
                        tags: parts[6] ? parts[6].split(';').map(tag => tag.trim()).filter(tag => tag) : []
                    };
                    
                    // 驗證學生ID
                    const student = window.studentManager ? window.studentManager.getStudent(bookData.studentId) : null;
                    if (!student) {
                        throw new Error(`找不到學生ID: ${bookData.studentId}`);
                    }
                    
                    // 驗證狀態
                    const validStatuses = ['to-read', 'reading', 'completed', 'abandoned'];
                    if (!validStatuses.includes(bookData.status)) {
                        throw new Error(`無效的狀態: ${bookData.status}`);
                    }
                    
                    // 驗證頁數
                    if (bookData.currentPage > bookData.totalPages && bookData.totalPages > 0) {
                        throw new Error('目前頁數不能超過總頁數');
                    }
                    
                    // 新增書籍
                    if (window.dataManager) {
                        const newBook = window.dataManager.addBook(bookData);
                        results.success.push({
                            line: i + 1,
                            book: newBook,
                            message: `成功新增: ${bookData.title}`
                        });
                    }
                    
                } catch (error) {
                    results.errors.push({
                        line: i + 1,
                        message: `第 ${i + 1} 行錯誤: ${error.message}`,
                        data: line
                    });
                }
            }
            
            // 顯示結果
            displayBatchResults(results);
            
            // 如果有成功的記錄，重新渲染書籍列表
            if (results.success.length > 0) {
                renderBooks();
                
                // 觸發事件通知統計模組更新
                window.dispatchEvent(new CustomEvent('booksBulkUpdated', {
                    detail: {
                        operation: 'batch-add',
                        bookIds: results.success.map(r => r.book.id)
                    }
                }));
            }
            
        } catch (error) {
            alert('CSV 檔案處理失敗: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// 解析CSV行，處理包含逗號的引號字段
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    
    result.push(current.trim());
    return result;
}

// 顯示批次處理結果
function displayBatchResults(results) {
    const resultsContent = document.getElementById('resultsContent');
    const batchResults = document.getElementById('batchResults');
    
    if (!resultsContent || !batchResults) return;
    
    let html = '';
    
    if (results.success.length > 0) {
        html += '<div class="success-results">';
        html += `<h5>✅ 成功新增 ${results.success.length} 本書</h5>`;
        html += '<ul>';
        results.success.forEach(result => {
            html += `<li>${result.message}</li>`;
        });
        html += '</ul>';
        html += '</div>';
    }
    
    if (results.errors.length > 0) {
        html += '<div class="error-results">';
        html += `<h5>❌ ${results.errors.length} 個錯誤</h5>`;
        html += '<ul>';
        results.errors.forEach(result => {
            html += `<li>${result.message}`;
            if (result.data) {
                html += `<br><small>資料: ${result.data}</small>`;
            }
            html += '</li>';
        });
        html += '</ul>';
        html += '</div>';
    }
    
    resultsContent.innerHTML = html;
    batchResults.style.display = 'block';
}

// 下載閱讀記錄為CSV格式
function downloadBooksCSV() {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    if (books.length === 0) {
        alert('沒有閱讀記錄可以下載');
        return;
    }
    
    // CSV標題行
    const headers = ['學生ID', '學生姓名', '書名', '作者', '總頁數', '目前頁數', '進度百分比', '狀態', '評分', '標籤', '筆記', '新增日期', '修改日期'];
    
    // 轉換書籍資料為CSV行
    const csvRows = [headers.join(',')];
    
    books.forEach(book => {
        const student = window.studentManager ? window.studentManager.getStudent(book.studentId) : { id: book.studentId, name: '未知學生' };
        const progress = book.totalPages > 0 ? Math.round((book.currentPage / book.totalPages) * 100) : 0;
        const statusText = getStatusText(book.status);
        const tags = book.tags ? book.tags.join(';') : '';
        const notes = book.notes ? book.notes.replace(/"/g, '""') : ''; // 處理引號
        
        const row = [
            book.studentId,
            `"${student.name}"`,
            `"${book.title.replace(/"/g, '""')}"`,
            `"${(book.author || '').replace(/"/g, '""')}"`,
            book.totalPages,
            book.currentPage,
            progress + '%',
            statusText,
            book.rating || 0,
            `"${tags}"`,
            `"${notes}"`,
            book.dateAdded ? new Date(book.dateAdded).toLocaleDateString('zh-TW') : '',
            book.dateModified ? new Date(book.dateModified).toLocaleDateString('zh-TW') : ''
        ];
        
        csvRows.push(row.join(','));
    });
    
    // 建立CSV檔案
    const csvContent = csvRows.join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' }); // 添加BOM以支援中文
    
    // 建立下載連結
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `閱讀記錄_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 處理批次新增表單提交
function handleBatchAddSubmit() {
    const activeTab = document.querySelector('.tab-content.active');
    
    if (activeTab.id === 'text-input') {
        processBatchTextInput();
    } else if (activeTab.id === 'csv-upload') {
        const fileInput = document.getElementById('csvFileInput');
        if (fileInput && fileInput.files.length > 0) {
            processBatchCSVUpload({ target: fileInput });
        } else {
            alert('請選擇CSV檔案');
        }
    }
}

// 切換批次新增的標籤頁
function switchBatchAddTab(tabId) {
    // 更新標籤按鈕狀態
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    
    // 更新內容顯示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabId).classList.add('active');
}

// 預覽CSV檔案
function previewCSVFile(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n').slice(0, 6); // 只顯示前5行
        
        const previewContent = document.getElementById('previewContent');
        const csvPreview = document.getElementById('csvPreview');
        
        if (previewContent) {
            let html = '<table class="preview-table">';
            lines.forEach((line, index) => {
                if (line.trim()) {
                    const parts = parseCSVLine(line);
                    html += '<tr>';
                    parts.forEach(part => {
                        html += `<td>${escapeHtml(part)}</td>`;
                    });
                    html += '</tr>';
                }
            });
            html += '</table>';
            
            if (lines.length >= 6) {
                html += '<p><small>只顯示前5行預覽...</small></p>';
            }
            
            previewContent.innerHTML = html;
            csvPreview.style.display = 'block';
        }
    };
    reader.readAsText(file);
}

// 更新導出函數
window.bookManager = {
    initBookManager,
    renderBooks,
    openBookModal,
    deleteBook,
    showBookDetail,
    quickUpdateProgress,
    bulkOperation,
    handleBookSubmit,
    handleRatingClick,
    setRating,
    getCurrentRating,
    openBatchAddModal,
    processBatchTextInput,
    processBatchCSVUpload,
    downloadBooksCSV,
    handleBatchAddSubmit,
    switchBatchAddTab,
    previewCSVFile
};
// 字體大小功能診斷工具
// 使用方法：在瀏覽器控制台中執行 diagnoseFontSize()

function diagnoseFontSize() {
    console.log('=== 字體大小功能診斷開始 ===');
    
    const results = {
        timestamp: new Date().toISOString(),
        checks: {},
        errors: [],
        recommendations: []
    };
    
    // 檢查1: 基本DOM元素
    console.log('1. 檢查基本DOM元素...');
    try {
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        if (fontSizeSelect) {
            results.checks.fontSizeSelect = {
                exists: true,
                value: fontSizeSelect.value,
                options: Array.from(fontSizeSelect.options).map(opt => opt.value)
            };
            console.log('✅ fontSizeSelect 元素存在');
        } else {
            results.errors.push('找不到 fontSizeSelect 元素');
            console.error('❌ 找不到 fontSizeSelect 元素');
        }
    } catch (error) {
        results.errors.push(`檢查 fontSizeSelect 時發生錯誤: ${error.message}`);
        console.error('❌ 檢查 fontSizeSelect 時發生錯誤:', error);
    }
    
    // 檢查2: CSS類別定義
    console.log('2. 檢查CSS類別定義...');
    try {
        const testElement = document.createElement('div');
        document.body.appendChild(testElement);
        
        // 測試各種字體大小類別
        const fontSizes = ['small', 'medium', 'large'];
        const cssClasses = {};
        
        fontSizes.forEach(size => {
            testElement.className = '';
            document.body.classList.add(`font-size-${size}`);
            
            // 檢查body是否有對應的class
            const hasClass = document.body.classList.contains(`font-size-${size}`);
            cssClasses[size] = hasClass;
            
            if (hasClass) {
                console.log(`✅ font-size-${size} CSS類別正常`);
            } else {
                results.errors.push(`font-size-${size} CSS類別無效`);
                console.error(`❌ font-size-${size} CSS類別無效`);
            }
            
            // 清理
            document.body.classList.remove(`font-size-${size}`);
        });
        
        results.checks.cssClasses = cssClasses;
        testElement.remove();
        
    } catch (error) {
        results.errors.push(`檢查CSS類別時發生錯誤: ${error.message}`);
        console.error('❌ 檢查CSS類別時發生錯誤:', error);
    }
    
    // 檢查3: localStorage功能
    console.log('3. 檢查localStorage功能...');
    try {
        const testKey = 'classReadingTracker_fontSize';
        const testValue = 'medium';
        
        // 測試儲存
        localStorage.setItem(testKey, testValue);
        const retrieved = localStorage.getItem(testKey);
        
        if (retrieved === testValue) {
            results.checks.localStorage = {
                read: true,
                write: true,
                currentValue: retrieved
            };
            console.log('✅ localStorage 讀寫功能正常');
        } else {
            results.errors.push('localStorage 讀寫功能異常');
            console.error('❌ localStorage 讀寫功能異常');
        }
        
    } catch (error) {
        results.errors.push(`檢查localStorage時發生錯誤: ${error.message}`);
        console.error('❌ 檢查localStorage時發生錯誤:', error);
    }
    
    // 檢查4: UI管理器模組
    console.log('4. 檢查UI管理器模組...');
    try {
        if (window.uiManager) {
            results.checks.uiManager = {
                loaded: true,
                functions: Object.keys(window.uiManager).filter(key => typeof window.uiManager[key] === 'function')
            };
            console.log('✅ UI管理器模組已載入');
            
            // 檢查關鍵函數
            const requiredFunctions = ['initUIManager', 'initializeFontSize', 'handleFontSizeChange'];
            requiredFunctions.forEach(funcName => {
                if (typeof window.uiManager[funcName] === 'function') {
                    console.log(`✅ ${funcName} 函數存在`);
                } else {
                    results.errors.push(`${funcName} 函數不存在`);
                    console.error(`❌ ${funcName} 函數不存在`);
                }
            });
        } else {
            results.errors.push('UI管理器模組未載入');
            console.error('❌ UI管理器模組未載入');
        }
    } catch (error) {
        results.errors.push(`檢查UI管理器時發生錯誤: ${error.message}`);
        console.error('❌ 檢查UI管理器時發生錯誤:', error);
    }
    
    // 檢查5: 錯誤處理器模組
    console.log('5. 檢查錯誤處理器模組...');
    try {
        if (window.errorHandler) {
            results.checks.errorHandler = {
                loaded: true,
                functions: Object.keys(window.errorHandler).filter(key => typeof window.errorHandler[key] === 'function')
            };
            console.log('✅ 錯誤處理器模組已載入');
        } else {
            results.errors.push('錯誤處理器模組未載入');
            console.error('❌ 錯誤處理器模組未載入');
        }
    } catch (error) {
        results.errors.push(`檢查錯誤處理器時發生錯誤: ${error.message}`);
        console.error('❌ 檢查錯誤處理器時發生錯誤:', error);
    }
    
    // 檢查6: 事件監聽器
    console.log('6. 檢查事件監聽器...');
    try {
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        if (fontSizeSelect) {
            // 創建一個臨時事件來測試
            const testEvent = new Event('change', { bubbles: true });
            fontSizeSelect.dispatchEvent(testEvent);
            
            // 如果沒有拋出錯誤，假設事件監聽器工作正常
            results.checks.eventListeners = true;
            console.log('✅ 事件監聽器工作正常');
        } else {
            results.errors.push('無法測試事件監聽器 (fontSizeSelect 不存在)');
            console.error('❌ 無法測試事件監聽器 (fontSizeSelect 不存在)');
        }
    } catch (error) {
        results.errors.push(`檢查事件監聽器時發生錯誤: ${error.message}`);
        console.error('❌ 檢查事件監聽器時發生錯誤:', error);
    }
    
    // 生成建議
    console.log('7. 生成修復建議...');
    if (results.errors.length === 0) {
        results.recommendations.push('✅ 所有檢查都通過，字體大小功能應該正常工作');
    } else {
        if (results.errors.includes('找不到 fontSizeSelect 元素')) {
            results.recommendations.push('🔧 檢查 HTML 中是否有 id="fontSizeSelect" 的元素');
        }
        if (results.errors.some(e => e.includes('CSS類別'))) {
            results.recommendations.push('🔧 檢查 styles.css 中是否定義了 font-size-* CSS類別');
        }
        if (results.errors.some(e => e.includes('localStorage'))) {
            results.recommendations.push('🔧 檢查瀏覽器是否允許使用 localStorage');
        }
        if (results.errors.some(e => e.includes('UI管理器'))) {
            results.recommendations.push('🔧 檢查 ui-manager.js 是否正確載入');
        }
        if (results.errors.some(e => e.includes('錯誤處理器'))) {
            results.recommendations.push('🔧 檢查 error-handler.js 是否正確載入');
        }
    }
    
    // 輸出結果
    console.log('=== 診斷結果摘要 ===');
    console.log('檢查項目:', Object.keys(results.checks).length);
    console.log('錯誤數量:', results.errors.length);
    console.log('建議數量:', results.recommendations.length);
    
    if (results.errors.length > 0) {
        console.error('發現的錯誤:');
        results.errors.forEach((error, index) => {
            console.error(`${index + 1}. ${error}`);
        });
    }
    
    if (results.recommendations.length > 0) {
        console.log('修復建議:');
        results.recommendations.forEach((recommendation, index) => {
            console.log(`${index + 1}. ${recommendation}`);
        });
    }
    
    console.log('=== 字體大小功能診斷完成 ===');
    console.log('詳細結果:', results);
    
    return results;
}

// 快速修復功能
function quickFixFontSize() {
    console.log('=== 開始快速修復字體大小功能 ===');
    
    try {
        // 1. 確保UI管理器已初始化
        if (window.uiManager && typeof window.uiManager.initUIManager === 'function') {
            window.uiManager.initUIManager();
            console.log('✅ UI管理器已重新初始化');
        }
        
        // 2. 確保字體大小已初始化
        if (window.uiManager && typeof window.uiManager.initializeFontSize === 'function') {
            window.uiManager.initializeFontSize();
            console.log('✅ 字體大小已重新初始化');
        }
        
        // 3. 重新設置事件監聽器
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        if (fontSizeSelect && window.uiManager && window.uiManager.handleFontSizeChange) {
            // 移除舊的事件監聽器
            fontSizeSelect.removeEventListener('change', window.uiManager.handleFontSizeChange);
            // 添加新的事件監聽器
            fontSizeSelect.addEventListener('change', window.uiManager.handleFontSizeChange);
            console.log('✅ 事件監聽器已重新設置');
        }
        
        // 4. 設置預設字體大小
        const savedFontSize = localStorage.getItem('classReadingTracker_fontSize') || 'medium';
        document.body.classList.remove('font-size-small', 'font-size-medium', 'font-size-large');
        document.body.classList.add(`font-size-${savedFontSize}`);
        if (fontSizeSelect) {
            fontSizeSelect.value = savedFontSize;
        }
        console.log(`✅ 預設字體大小已設置為: ${savedFontSize}`);
        
        console.log('=== 快速修復完成 ===');
        console.log('請重新測試字體大小調整功能');
        
    } catch (error) {
        console.error('❌ 快速修復失敗:', error);
    }
}

// 測試字體大小變更
function testFontSizeChange() {
    console.log('=== 測試字體大小變更 ===');
    
    const fontSizeSelect = document.getElementById('fontSizeSelect');
    if (!fontSizeSelect) {
        console.error('❌ 找不到字體大小選擇器');
        return;
    }
    
    const testSizes = ['small', 'medium', 'large'];
    
    testSizes.forEach(size => {
        console.log(`測試字體大小: ${size}`);
        
        // 設置值
        fontSizeSelect.value = size;
        
        // 觸發事件
        const event = new Event('change', { bubbles: true });
        fontSizeSelect.dispatchEvent(event);
        
        // 檢查結果
        setTimeout(() => {
            const hasClass = document.body.classList.contains(`font-size-${size}`);
            const savedValue = localStorage.getItem('classReadingTracker_fontSize');
            
            console.log(`  - CSS類別: ${hasClass ? '✅' : '❌'}`);
            console.log(`  - localStorage: ${savedValue === size ? '✅' : '❌'}`);
        }, 100);
    });
    
    console.log('=== 測試完成 ===');
}

// 導出函數到全局作用域
window.diagnoseFontSize = diagnoseFontSize;
window.quickFixFontSize = quickFixFontSize;
window.testFontSizeChange = testFontSizeChange;

console.log('字體大小診斷工具已載入');
console.log('使用方法:');
console.log('- diagnoseFontSize() - 完整診斷');
console.log('- quickFixFontSize() - 快速修復');
console.log('- testFontSizeChange() - 測試功能');
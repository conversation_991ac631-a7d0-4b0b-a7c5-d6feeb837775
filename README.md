# 班級閱讀進度追蹤系統

一個專為小學班級設計的網頁版閱讀進度追蹤系統，幫助老師管理全班27位同學的閱讀進度，培養學生良好的閱讀習慣。系統使用歷史人物作為學生姓名，讓學習更有趣味性。

## 🌟 主要功能

### 👥 班級管理
- **27位學生**：預設27位歷史人物作為學生（孔子、老子、孟子等）
- **學生名單管理**：可以自訂修改學生的姓名和全名
- **學生選擇**：每筆閱讀記錄都需指定對應的學生
- **學生篩選**：可以篩選特定學生的閱讀記錄
- **閱讀之星**：顯示班級閱讀表現最佳的學生排行榜

### 📚 閱讀記錄管理
- **新增記錄**：為指定學生新增閱讀記錄
- **批次新增**：支援文字輸入和CSV檔案上傳兩種方式批次新增閱讀記錄
- **狀態管理**：將書籍分為「待讀」、「閱讀中」、「已完成」、「暫停」四種狀態
- **進度追蹤**：記錄當前閱讀頁數，自動計算閱讀進度百分比
- **封面圖片**：支援添加書籍封面圖片 URL
- **標籤分類**：使用標籤對書籍進行分類管理
- **記錄下載**：將所有閱讀記錄匯出為CSV格式檔案，方便進行資料分析和備份

### ⭐ 評分與筆記
- **星級評分**：支援 1-5 星評分系統
- **閱讀筆記**：學生可以為每本書添加閱讀心得和筆記
- **詳細檢視**：點擊書籍卡片查看完整的閱讀記錄資訊

### 📊 班級統計與目標
- **班級統計**：顯示全班總書籍數、已完成數、閱讀中數量、總閱讀頁數、活躍學生數
- **班級目標**：設定班級年度閱讀目標（預設270本，平均每人10本）
- **進度視覺化**：使用進度條直觀顯示班級閱讀進度和目標達成情況
- **學生排行**：「閱讀之星」功能顯示表現優秀的學生

### 🔍 搜尋與篩選
- **即時搜尋**：支援按書名、作者、學生姓名、標籤搜尋
- **狀態篩選**：快速篩選不同狀態的閱讀記錄
- **學生篩選**：可選擇特定學生或顯示全班記錄
- **多種排序**：支援按學生、書名、作者、進度、評分、新增日期排序

### 🎨 介面與體驗
- **響應式設計**：完美適配桌面、平板、手機等各種設備
- **網格/列表視圖**：支援兩種檢視模式切換
- **現代化 UI**：採用漸層背景、毛玻璃效果等現代設計元素
- **流暢動畫**：豐富的過渡動畫提升使用體驗
- **側邊欄字體調整**：提供大、中、小三種字體大小選項，主要調整側邊欄班級統計、篩選器和目標設定等內容的字體大小

### 💾 資料管理
- **本地儲存**：使用瀏覽器 localStorage 儲存班級資料
- **資料匯出**：支援將班級閱讀資料匯出為 JSON 格式備份
- **資料匯入**：支援從備份檔案恢復班級資料
- **批次新增**：支援透過文字輸入或CSV檔案批次新增閱讀記錄
- **CSV下載**：將閱讀記錄匯出為CSV格式，方便使用Excel等軟體進行分析
- **鍵盤快捷鍵**：支援常用操作的快捷鍵
- **錯誤處理**：增強的錯誤處理機制，提供更好的用戶體驗

## 👥 班級學生名單

本系統預設了27位中國歷史人物作為學生：

| 座號 | 姓名 | 全名 | 座號 | 姓名 | 全名 |
|------|------|------|------|------|------|
| 01 | 孔子 | 孔丘 | 15 | 辛棄疾 | 辛幼安 |
| 02 | 老子 | 李耳 | 16 | 朱熹 | 朱元晦 |
| 03 | 孟子 | 孟軻 | 17 | 王陽明 | 王守仁 |
| 04 | 莊子 | 莊周 | 18 | 曹雪芹 | 曹霑 |
| 05 | 荀子 | 荀況 | 19 | 吳承恩 | 吳承恩 |
| 06 | 墨子 | 墨翟 | 20 | 施耐庵 | 施耐庵 |
| 07 | 韓非子 | 韓非 | 21 | 羅貫中 | 羅本 |
| 08 | 司馬遷 | 司馬遷 | 22 | 關漢卿 | 關漢卿 |
| 09 | 諸葛亮 | 諸葛亮 | 23 | 馬致遠 | 馬致遠 |
| 10 | 李白 | 李太白 | 24 | 鄭板橋 | 鄭燮 |
| 11 | 杜甫 | 杜子美 | 25 | 紀曉嵐 | 紀昀 |
| 12 | 白居易 | 白樂天 | 26 | 林則徐 | 林則徐 |
| 13 | 蘇軾 | 蘇東坡 | 27 | 梁啟超 | 梁啟超 |
| 14 | 李清照 | 李清照 | | | |

## 🚀 快速開始

### 1. 開啟應用
直接在瀏覽器中開啟 `index.html` 檔案即可使用。

### 2. 載入示例資料（可選）
如果想快速體驗功能，可以：
1. 點擊「匯入」按鈕
2. 選擇 `sample-data.json` 檔案
3. 確認匯入示例資料

### 3. 開始使用
1. 點擊「新增閱讀記錄」按鈕為學生添加閱讀記錄
2. 選擇學生並填寫書籍資訊
3. 在書籍卡片上更新閱讀進度
4. 使用篩選和搜尋功能管理班級閱讀記錄
5. 查看「閱讀之星」了解班級閱讀表現
6. 使用「批次新增」功能一次添加多筆閱讀記錄
7. 使用「下載記錄」功能將閱讀記錄匯出為CSV檔案

## 📱 使用說明

### 新增閱讀記錄
1. 點擊右上角「新增閱讀記錄」按鈕
2. 選擇學生（必選）
3. 填寫書籍資訊（書名為必填）
4. 選擇閱讀狀態
5. 添加標籤（用逗號分隔）
6. 設定評分和撰寫筆記
7. 點擊「儲存」完成新增

### 更新進度
1. 點擊書籍卡片上的「編輯」按鈕
2. 修改「目前頁數」
3. 系統會自動計算進度百分比
4. 如果讀完整本書，可將狀態改為「已完成」

### 設定班級閱讀目標
1. 在左側邊欄點擊「設定班級目標」按鈕
2. 輸入班級年度閱讀目標數量（建議每人10本 = 270本）
3. 系統會根據全班已完成書籍數量顯示進度

### 管理學生名單
1. 在左側邊欄點擊「管理學生名單」按鈕
2. 修改學生的姓名和全名（座號無法修改）
3. 點擊「儲存修改」保存變更
4. 如需恢復預設名單，點擊「重置為預設名單」

### 批次新增閱讀記錄
1. 點擊右上角「批次新增」按鈕
2. 選擇輸入方式：
   - **文字輸入**：直接在文字框中輸入多筆書籍資料
   - **CSV檔案上傳**：上傳CSV格式的檔案進行批次新增
3. 按照指定格式輸入資料（格式說明請見下方）
4. 點擊「處理」按鈕
5. 查看處理結果，系統會顯示成功和失敗的記錄數量

### 下載閱讀記錄
1. 點擊右上角「下載記錄」按鈕
2. 系統會自動生成包含所有閱讀記錄的CSV檔案
3. 檔案會自動下載，命名格式為「閱讀記錄_YYYY-MM-DD.csv」
4. 可使用Excel或其他試算表軟體開啟和分析

### 搜尋與篩選
- **學生選擇**：在頂部選擇特定學生查看其閱讀記錄
- **搜尋**：在搜尋框輸入書名、作者或學生姓名
- **狀態篩選**：點擊左側邊欄的狀態按鈕
- **閱讀之星**：點擊「閱讀之星」查看班級排行榜
- **排序**：使用右上角的排序下拉選單

### 字體大小調整
- **字體大小選擇**：在右上角選擇字體大小（小、中、大）
- **側邊欄調整**：主要調整左側邊欄的班級閱讀統計、篩選器、班級目標等內容的字體大小
- **自動儲存**：選擇的字體大小會自動儲存，下次開啟時會記住您的設定
- **即時生效**：更改字體大小後，側邊欄內容會立即調整
- **響應式適配**：不同字體大小會在不同設備上自動調整，確保最佳顯示效果

### 字體大小調整使用方式
1. 在右上角選擇字體大小（小、中、大）
2. 左側邊欄的班級閱讀統計、篩選器和班級目標等內容會立即調整為選擇的字體大小
3. 設定會自動儲存，下次開啟時會恢復之前的設定

### 批次新增格式說明

#### 文字輸入格式
```
學生ID,書名,作者,總頁數,目前頁數,狀態,標籤
01,論語,孔子,200,150,reading,經典;哲學
02,道德經,老子,100,80,reading,經典;哲學
```

#### CSV檔案格式
CSV檔案應包含以下欄位（順序可調整）：
- 學生ID（必填）：學生的座號，如01、02等
- 書名（必填）：書籍的名稱
- 作者（可選）：書籍的作者
- 總頁數（可選）：書籍的總頁數，預設為0
- 目前頁數（可選）：當前閱讀頁數，預設為0
- 狀態（可選）：to-read（待讀）、reading（閱讀中）、completed（已完成）、abandoned（暫停），預設為to-read
- 標籤（可選）：多個標籤用分號(;)分隔

#### CSV檔案範例
```csv
學生ID,書名,作者,總頁數,目前頁數,狀態,標籤
01,論語,孔子,200,150,reading,經典;哲學
02,道德經,老子,100,80,reading,經典;哲學
03,史記,司馬遷,500,200,reading,歷史;經典
```

#### 注意事項
- 學生ID必須存在於系統中，否則會新增失敗
- 狀態必須是有效值（to-read、reading、completed、abandoned）
- 目前頁數不能超過總頁數
- 標籤使用分號(;)分隔，不要使用逗號
- 支援引號包裹欄位內容，可處理包含逗號的書名或作者名

## ⌨️ 鍵盤快捷鍵

- `Ctrl/Cmd + N`：新增閱讀記錄
- `Ctrl/Cmd + F`：聚焦搜尋框
- `ESC`：關閉模態框

## 💾 資料備份

### 匯出班級資料
1. 點擊右上角「匯出」按鈕
2. 系統會下載包含全班閱讀記錄、學生名單和設定的 JSON 檔案
3. 建議定期備份以防資料遺失
4. 備份檔案命名格式：`reading-tracker-backup-YYYY-MM-DD.json`

### 匯入班級資料
1. 點擊右上角「匯入」按鈕
2. 選擇之前匯出的 JSON 備份檔案
3. 確認匯入（會覆蓋現有資料）
4. 系統會自動驗證檔案格式和數據完整性

### 備份檔案格式
匯出的 JSON 檔案包含以下資訊：
```json
{
  "books": [
    {
      "id": "書籍唯一ID",
      "studentId": "學生座號",
      "title": "書名",
      "author": "作者",
      "totalPages": 總頁數,
      "currentPage": 目前頁數,
      "status": "閱讀狀態",
      "rating": 評分,
      "tags": ["標籤1", "標籤2"],
      "notes": "閱讀筆記",
      "dateAdded": "新增日期",
      "dateModified": "修改日期"
    }
  ],
  "students": [
    {
      "id": "學生座號",
      "name": "姓名",
      "fullName": "全名"
    }
  ],
  "settings": {
    "yearlyGoal": 年度目標數量
  },
  "exportDate": "匯出日期",
  "version": "檔案版本"
}
```

### 資料安全
- 所有資料都儲存在瀏覽器的 localStorage 中
- 匯出功能讓您可以隨時備份重要資料
- 匯入前會確認是否覆蓋現有資料
- 系統會自動驗證匯入檔案的完整性
- 建議定期備份，尤其是在進行大量資料修改後

## 🎨 自訂功能

### 匯入匯出功能
匯入/導出功能現在已經預設啟用，按鈕會自動顯示在右上角的操作區域。如果您需要自訂或禁用此功能，可以修改 `script.js` 中的相關代碼。

### 修改班級目標預設值
在 `script.js` 中修改：
```javascript
let userSettings = JSON.parse(localStorage.getItem('classReadingTracker_settings')) || {
    yearlyGoal: 270  // 修改這個數值（建議27人 × 10本 = 270本）
};
```

### 修改學生名單
現在可以直接在網頁介面中修改學生名單：
1. 點擊「管理學生名單」按鈕
2. 在彈出的視窗中修改學生姓名和全名
3. 點擊「儲存修改」保存變更

如需程式碼層面的修改，請在 `script.js` 中修改 `defaultStudents` 陣列：
```javascript
const defaultStudents = [
    { id: '01', name: '新姓名', fullName: '新全名' },
    // ... 其他學生
];
```

## 🌐 瀏覽器支援

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📝 技術特色

- **純前端實現**：無需伺服器，可離線使用
- **響應式設計**：適配各種螢幕尺寸，支援桌面和平板使用
- **現代 CSS**：使用 CSS Grid、Flexbox、CSS 變數等現代特性
- **ES6+ JavaScript**：使用現代 JavaScript 語法
- **本地儲存**：班級資料儲存在瀏覽器本地，保護隱私
- **無依賴**：不依賴任何第三方 JavaScript 框架
- **教育導向**：專為小學班級設計的使用者介面和功能
- **模組化架構**：採用模組化設計，便於維護和擴展
- **錯誤處理**：增強的錯誤處理機制，提供更好的用戶體驗
- **測試框架**：包含完整的測試框架，確保功能穩定性

## 🧪 測試與故障排除

### 測試功能
系統包含多個測試文件，確保各項功能正常運作：

- **`test-import-export.html`**：專門測試導入/導出功能
- **`unified-test.html`**：綜合測試所有功能
- **`test-framework.js`**：測試框架核心

### 常見問題解決

#### 學生選擇器問題
如果學生選擇器沒有顯示學生名單：
1. 點擊「診斷修復」按鈕
2. 查看控制台日誌了解詳細錯誤信息
3. 確認瀏覽器支援 localStorage 功能

#### 導入/導出問題
如果導入/導出功能無法使用：
1. 確認瀏覽器允許文件下載和上傳
2. 檢查 JSON 檔案格式是否正確
3. 嘗試使用 `test-import-export.html` 進行功能測試

#### 資料遺失問題
如果發現資料遺失：
1. 檢查是否有最近的備份檔案
2. 確認瀏覽器沒有清除 localStorage
3. 使用匯入功能恢復備份資料

### 錯誤處理機制
系統包含完整的錯誤處理機制：
- **自動錯誤捕獲**：自動捕獲和記錄運行時錯誤
- **用戶友好提示**：提供清晰的錯誤信息
- **自動恢復**：在可能的情况下自動恢復功能
- **診斷工具**：提供診斷工具幫助解決問題

## 🔮 未來規劃

- [x] 批次新增閱讀記錄功能
- [x] 下載閱讀記錄為CSV格式
- [ ] 學生個人閱讀報告
- [ ] 班級閱讀統計圖表
- [ ] 閱讀時間追蹤功能
- [ ] 閱讀成就徽章系統
- [ ] 書籍推薦功能
- [ ] 家長查看介面
- [ ] 匯出學生個人報告
- [ ] 深色模式
- [ ] PWA 支援（離線使用）
- [ ] 多班級管理功能
- [ ] 雲端同步功能
- [ ] 行動應用版本

## 📄 授權

本專案採用 MIT 授權條款。

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改善這個專案！

---

## 🎓 教育價值

這個系統不僅是一個閱讀追蹤工具，更具有以下教育價值：

1. **歷史文化學習**：通過歷史人物姓名，學生可以了解中國古代文化名人
2. **閱讀習慣培養**：系統化的記錄幫助學生建立良好的閱讀習慣
3. **競爭激勵機制**：「閱讀之星」排行榜激發學生閱讀興趣
4. **數據化管理**：老師可以清楚掌握每位學生的閱讀進度
5. **自我反思能力**：筆記功能培養學生的思考和表達能力

**讓我們一起培養愛閱讀的下一代！** 📚🌟

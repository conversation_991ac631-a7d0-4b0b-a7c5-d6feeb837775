<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班級閱讀進度追蹤系統</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-graduation-cap"></i>
                    班級閱讀進度追蹤系統 📚✨
                </h1>
                <div class="header-actions">
                    <div class="student-selector">
                        <select id="studentSelect">
                            <option value="">選擇學生</option>
                        </select>
                    </div>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="搜尋書籍或學生...">
                    </div>
                    <div class="font-size-selector">
                        <select id="fontSizeSelect">
                            <option value="small">小字體</option>
                            <option value="medium" selected>中字體</option>
                            <option value="large">大字體</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" id="addBookBtn">
                        <i class="fas fa-plus"></i>
                        新增閱讀記錄 📖
                    </button>
                    <button class="btn btn-secondary" id="batchAddBtn">
                        <i class="fas fa-plus-circle"></i>
                        批次新增 📚
                    </button>
                    <button class="btn btn-secondary" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        下載記錄 📄
                    </button>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 側邊欄 -->
            <aside class="sidebar">
                <!-- 班級統計面板 -->
                <div class="stats-panel">
                    <h3>班級閱讀統計 📊</h3>
                    <div class="stat-item">
                        <span class="stat-label">全班總書籍</span>
                        <span class="stat-value" id="totalBooks">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">已完成</span>
                        <span class="stat-value" id="completedBooks">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">閱讀中</span>
                        <span class="stat-value" id="readingBooks">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">全班總頁數</span>
                        <span class="stat-value" id="totalPages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">活躍學生</span>
                        <span class="stat-value" id="activeStudents">0</span>
                    </div>
                </div>

                <!-- 篩選器 -->
                <div class="filter-panel">
                    <h3>篩選 🔍</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-status="all">全部記錄</button>
                        <button class="filter-btn" data-status="to-read">待讀</button>
                        <button class="filter-btn" data-status="reading">閱讀中</button>
                        <button class="filter-btn" data-status="completed">已完成</button>
                        <button class="filter-btn" data-status="abandoned">暫停</button>
                    </div>
                    <div class="student-filter">
                        <h4>學生篩選 👥</h4>
                        <button class="filter-btn" id="showAllStudents">👨‍👩‍👧‍👦 顯示全班</button>
                        <button class="filter-btn" id="showTopReaders">⭐ 閱讀之星</button>
                    </div>
                </div>

                <!-- 班級目標 -->
                <div class="goal-panel">
                    <h3>班級閱讀目標 🎯</h3>
                    <div class="goal-progress">
                        <div class="goal-text">
                            <span id="goalProgress">0</span> / <span id="goalTarget">270</span> 本
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="goalProgressBar"></div>
                        </div>
                        <div class="goal-subtitle">
                            平均每人 <span id="avgPerStudent">10</span> 本
                        </div>
                    </div>
                    <button class="btn btn-secondary btn-small" id="setGoalBtn">⚙️ 設定班級目標</button>
                    <button class="btn btn-secondary btn-small" id="manageStudentsBtn">👥 管理學生名單</button>
                    <button class="btn btn-secondary btn-small" onclick="diagnoseStudentSelector()" style="background: #28a745;">🔧 診斷修復</button>
                    <button class="btn btn-secondary btn-small" onclick="diagnoseFontSize()" style="background: #17a2b8;">🔤 字體診斷</button>
                </div>
            </aside>

            <!-- 主內容區 -->
            <main class="content">
                <div class="content-header">
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <div class="sort-controls">
                        <select id="sortSelect">
                            <option value="student">按學生排序</option>
                            <option value="title">按書名排序</option>
                            <option value="author">按作者排序</option>
                            <option value="progress">按進度排序</option>
                            <option value="rating">按評分排序</option>
                            <option value="dateAdded">按新增日期排序</option>
                        </select>
                    </div>
                </div>

                <!-- 書籍列表 -->
                <div class="books-container" id="booksContainer">
                    <!-- 書籍卡片將在這裡動態生成 -->
                </div>

                <!-- 空狀態 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <i class="fas fa-book-open"></i>
                    <h3>還沒有任何閱讀記錄呢！ 📚</h3>
                    <p>點擊「新增閱讀記錄」開始記錄學生的閱讀進度吧！ ✨</p>
                </div>
            </main>
        </div>
    </div>

    <!-- 新增/編輯書籍模態框 -->
    <div class="modal" id="bookModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">新增閱讀記錄</h2>
                <button class="close-btn" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="bookForm">
                <div class="form-group">
                    <label for="studentId">學生 *</label>
                    <select id="studentId" required>
                        <option value="">請選擇學生</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="bookTitle">書名 *</label>
                    <input type="text" id="bookTitle" required>
                </div>
                <div class="form-group">
                    <label for="bookAuthor">作者</label>
                    <input type="text" id="bookAuthor">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="totalPages">總頁數</label>
                        <input type="number" id="totalPages" min="1">
                    </div>
                    <div class="form-group">
                        <label for="currentPage">目前頁數</label>
                        <input type="number" id="currentPage" min="0">
                    </div>
                </div>
                <div class="form-group">
                    <label for="bookStatus">狀態</label>
                    <select id="bookStatus">
                        <option value="to-read">待讀</option>
                        <option value="reading">閱讀中</option>
                        <option value="completed">已完成</option>
                        <option value="abandoned">暫停</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="bookCover">封面圖片 URL</label>
                    <input type="url" id="bookCover">
                </div>
                <div class="form-group">
                    <label for="bookTags">標籤（用逗號分隔）</label>
                    <input type="text" id="bookTags" placeholder="例如：小說,科幻,推理">
                </div>
                <div class="form-group">
                    <label>評分</label>
                    <div class="rating-input" id="ratingInput">
                        <i class="fas fa-star" data-rating="1"></i>
                        <i class="fas fa-star" data-rating="2"></i>
                        <i class="fas fa-star" data-rating="3"></i>
                        <i class="fas fa-star" data-rating="4"></i>
                        <i class="fas fa-star" data-rating="5"></i>
                    </div>
                </div>
                <div class="form-group">
                    <label for="bookNotes">筆記</label>
                    <textarea id="bookNotes" rows="4" placeholder="寫下您的閱讀心得..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button type="submit" class="btn btn-primary">儲存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 設定目標模態框 -->
    <div class="modal" id="goalModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>設定班級閱讀目標</h2>
                <button class="close-btn" id="closeGoalModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="goalForm">
                <div class="form-group">
                    <label for="yearlyGoal">班級年度目標（本）</label>
                    <input type="number" id="yearlyGoal" min="27" value="270" step="27">
                    <small>建議：每位學生平均10本書 = 270本</small>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelGoalBtn">取消</button>
                    <button type="submit" class="btn btn-primary">儲存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 書籍詳情模態框 -->
    <div class="modal" id="bookDetailModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h2 id="detailTitle">書籍詳情</h2>
                <button class="close-btn" id="closeDetailModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="book-detail-content" id="bookDetailContent">
                <!-- 詳情內容將在這裡動態生成 -->
            </div>
        </div>
    </div>

    <!-- 學生名單管理模態框 -->
    <div class="modal" id="studentsModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h2>管理學生名單</h2>
                <button class="close-btn" id="closeStudentsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="students-management">
                <div class="students-header">
                    <p>您可以修改學生的姓名和全名。座號無法修改以保持資料一致性。</p>
                    <div class="students-actions">
                        <button class="btn btn-secondary" id="resetStudentsBtn">
                            <i class="fas fa-undo"></i>
                            重置為預設名單
                        </button>
                        <button class="btn btn-primary" id="saveStudentsBtn">
                            <i class="fas fa-save"></i>
                            儲存修改
                        </button>
                    </div>
                </div>
                <div class="students-grid" id="studentsGrid">
                    <!-- 學生編輯表格將在這裡動態生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 批次新增閱讀記錄模態框 -->
    <div class="modal" id="batchAddModal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h2>批次新增閱讀記錄</h2>
                <button class="close-btn" id="closeBatchAddModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="batch-add-content">
                <div class="batch-instructions">
                    <h3>使用說明</h3>
                    <p>您可以透過以下兩種方式批次新增閱讀記錄：</p>
                    <ol>
                        <li>在下方文字框中輸入多本書籍的資料，每行一本書</li>
                        <li>上傳 CSV 檔案（格式：學生ID,書名,作者,總頁數,目前頁數,狀態,標籤）</li>
                    </ol>
                    <p><strong>狀態說明：</strong> to-read（待讀）、reading（閱讀中）、completed（已完成）、abandoned（暫停）</p>
                </div>
                
                <div class="batch-input-methods">
                    <div class="method-tabs">
                        <button class="tab-btn active" data-tab="text-input">文字輸入</button>
                        <button class="tab-btn" data-tab="csv-upload">CSV 上傳</button>
                    </div>
                    
                    <div class="tab-content active" id="text-input">
                        <div class="form-group">
                            <label for="batchTextInput">書籍資料（每行一本）</label>
                            <textarea id="batchTextInput" rows="15" placeholder="格式：學生ID,書名,作者,總頁數,目前頁數,狀態,標籤

例如：
01,紅樓夢,曹雪芹,1200,350,reading,古典文學
02,西遊記,吳承恩,800,200,reading,古典文學
03,水滸傳,施耐庵,1000,0,to-read,古典文學"></textarea>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="csv-upload">
                        <div class="form-group">
                            <label for="csvFileInput">選擇 CSV 檔案</label>
                            <input type="file" id="csvFileInput" accept=".csv">
                            <small>CSV 格式：學生ID,書名,作者,總頁數,目前頁數,狀態,標籤</small>
                        </div>
                        <div class="csv-preview" id="csvPreview" style="display: none;">
                            <h4>檔案預覽</h4>
                            <div class="preview-content" id="previewContent"></div>
                        </div>
                    </div>
                </div>
                
                <div class="batch-results" id="batchResults" style="display: none;">
                    <h4>處理結果</h4>
                    <div class="results-content" id="resultsContent"></div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancelBatchAddBtn">取消</button>
                <button type="button" class="btn btn-primary" id="processBatchAddBtn">處理資料</button>
            </div>
        </div>
    </div>

    <!-- 模組化腳本文件 -->
    <script src="error-handler.js"></script>
    <script src="data-manager.js"></script>
    <script src="student-manager.js"></script>
    <script src="book-manager.js"></script>
    <script src="ui-manager.js"></script>
    <script src="modal-manager.js"></script>
    <script src="stats-manager.js"></script>
    <script src="app.js"></script>
    <script src="script.js"></script>
    <script src="diagnose-font-size.js"></script>
</body>
</html>

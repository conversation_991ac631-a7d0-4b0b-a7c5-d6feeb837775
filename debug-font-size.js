// 字體大小錯誤調試工具
console.log('=== 字體大小錯誤調試工具 ===');

// 1. 檢查錯誤處理器狀態
function checkErrorHandler() {
    console.log('1. 檢查錯誤處理器狀態...');
    
    if (typeof window.errorHandler === 'undefined') {
        console.error('❌ window.errorHandler 不存在');
        return false;
    }
    
    console.log('✅ window.errorHandler 存在');
    console.log('錯誤處理器內容:', Object.keys(window.errorHandler));
    
    // 檢查關鍵函數
    const requiredFunctions = ['handleError', 'logEvent', 'ErrorTypes', 'ErrorSeverity'];
    let allExists = true;
    
    requiredFunctions.forEach(funcName => {
        if (typeof window.errorHandler[funcName] === 'undefined') {
            console.error(`❌ window.errorHandler.${funcName} 不存在`);
            allExists = false;
        } else {
            console.log(`✅ window.errorHandler.${funcName} 存在 (類型: ${typeof window.errorHandler[funcName]})`);
        }
    });
    
    return allExists;
}

// 2. 檢查UI管理器狀態
function checkUIManager() {
    console.log('2. 檢查UI管理器狀態...');
    
    if (typeof window.uiManager === 'undefined') {
        console.error('❌ window.uiManager 不存在');
        return false;
    }
    
    console.log('✅ window.uiManager 存在');
    console.log('UI管理器內容:', Object.keys(window.uiManager));
    
    // 檢查關鍵函數
    const requiredFunctions = ['handleFontSizeChange', 'initializeFontSize'];
    let allExists = true;
    
    requiredFunctions.forEach(funcName => {
        if (typeof window.uiManager[funcName] === 'undefined') {
            console.error(`❌ window.uiManager.${funcName} 不存在`);
            allExists = false;
        } else {
            console.log(`✅ window.uiManager.${funcName} 存在`);
        }
    });
    
    return allExists;
}

// 3. 檢查字體大小選擇器
function checkFontSizeSelector() {
    console.log('3. 檢查字體大小選擇器...');
    
    const fontSizeSelect = document.getElementById('fontSizeSelect');
    if (!fontSizeSelect) {
        console.error('❌ fontSizeSelect 元素不存在');
        return false;
    }
    
    console.log('✅ fontSizeSelect 元素存在');
    console.log('當前值:', fontSizeSelect.value);
    console.log('選項數量:', fontSizeSelect.options.length);
    
    // 檢查事件監聽器
    const listeners = getEventListeners ? getEventListeners(fontSizeSelect) : '無法檢查事件監聽器';
    console.log('事件監聽器:', listeners);
    
    return true;
}

// 4. 測試字體大小變更（安全版本）
function testFontSizeChangeSafe() {
    console.log('4. 測試字體大小變更（安全版本）...');
    
    try {
        const fontSizeSelect = document.getElementById('fontSizeSelect');
        if (!fontSizeSelect) {
            console.error('❌ 找不到字體大小選擇器');
            return;
        }
        
        console.log('測試前狀態:');
        console.log('- 選擇器值:', fontSizeSelect.value);
        console.log('- body類別:', Array.from(document.body.classList).filter(c => c.startsWith('font-size-')));
        console.log('- localStorage值:', localStorage.getItem('classReadingTracker_fontSize'));
        
        // 手動調用處理函數（如果存在）
        if (window.uiManager && typeof window.uiManager.handleFontSizeChange === 'function') {
            console.log('手動調用 handleFontSizeChange...');
            
            // 創建模擬事件
            const mockEvent = {
                target: {
                    value: 'large'
                }
            };
            
            // 設置選擇器值
            fontSizeSelect.value = 'large';
            
            // 調用處理函數
            window.uiManager.handleFontSizeChange(mockEvent);
            
            console.log('測試後狀態:');
            console.log('- 選擇器值:', fontSizeSelect.value);
            console.log('- body類別:', Array.from(document.body.classList).filter(c => c.startsWith('font-size-')));
            console.log('- localStorage值:', localStorage.getItem('classReadingTracker_fontSize'));
            
        } else {
            console.error('❌ handleFontSizeChange 函數不存在');
        }
        
    } catch (error) {
        console.error('❌ 測試字體大小變更時發生錯誤:', error);
        console.error('錯誤堆棧:', error.stack);
    }
}

// 5. 檢查模組載入順序
function checkModuleLoadOrder() {
    console.log('5. 檢查模組載入順序...');
    
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    console.log('腳本載入順序:');
    scripts.forEach((script, index) => {
        console.log(`${index + 1}. ${script.src.split('/').pop()}`);
    });
}

// 6. 執行完整診斷
function runFullDiagnosis() {
    console.log('=== 開始完整診斷 ===');
    
    const results = {
        errorHandler: checkErrorHandler(),
        uiManager: checkUIManager(),
        fontSizeSelector: checkFontSizeSelector()
    };
    
    checkModuleLoadOrder();
    
    console.log('=== 診斷結果 ===');
    console.log('錯誤處理器:', results.errorHandler ? '✅ 正常' : '❌ 異常');
    console.log('UI管理器:', results.uiManager ? '✅ 正常' : '❌ 異常');
    console.log('字體大小選擇器:', results.fontSizeSelector ? '✅ 正常' : '❌ 異常');
    
    if (results.errorHandler && results.uiManager && results.fontSizeSelector) {
        console.log('🎉 所有檢查通過，開始安全測試...');
        testFontSizeChangeSafe();
    } else {
        console.log('⚠️ 發現問題，請檢查上述錯誤');
    }
    
    return results;
}

// 7. 監聽錯誤事件
window.addEventListener('error', function(event) {
    console.error('🚨 捕獲到全局錯誤:', {
        message: event.message,
        filename: event.filename,
        line: event.lineno,
        column: event.colno,
        error: event.error
    });
});

// 8. 監聽未處理的Promise拒絕
window.addEventListener('unhandledrejection', function(event) {
    console.error('🚨 捕獲到未處理的Promise拒絕:', event.reason);
});

// 導出函數供控制台使用
window.debugFontSize = {
    checkErrorHandler,
    checkUIManager,
    checkFontSizeSelector,
    testFontSizeChangeSafe,
    checkModuleLoadOrder,
    runFullDiagnosis
};

console.log('=== 調試工具已載入 ===');
console.log('使用方法：');
console.log('- window.debugFontSize.runFullDiagnosis() - 執行完整診斷');
console.log('- window.debugFontSize.testFontSizeChangeSafe() - 安全測試字體大小變更');
console.log('- window.debugFontSize.checkErrorHandler() - 檢查錯誤處理器');

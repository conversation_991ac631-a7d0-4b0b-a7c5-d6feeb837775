// 模態框管理模組 - 負責所有模態框的管理和操作

// 初始化模態框管理器
function initModalManager() {
    console.log('初始化模態框管理器...');
    setupModalEventListeners();
}

// 設置模態框事件監聽器
function setupModalEventListeners() {
    // 監聽打開學生管理模態框事件
    window.addEventListener('openStudentsModal', () => {
        openStudentsModalHandler();
    });
}

// 顯示模態框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

// 關閉模態框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        if (modalId === 'bookModal') {
            // 重置表單
            const bookForm = document.getElementById('bookForm');
            if (bookForm) {
                bookForm.reset();
            }
            
            // 重置評分
            if (window.bookManager) {
                window.bookManager.setRating(0);
            }
        } else if (modalId === 'batchAddModal') {
            // 重置批次新增表單
            const batchTextInput = document.getElementById('batchTextInput');
            const csvFileInput = document.getElementById('csvFileInput');
            const batchResults = document.getElementById('batchResults');
            const csvPreview = document.getElementById('csvPreview');
            
            if (batchTextInput) {
                batchTextInput.value = '';
            }
            
            if (csvFileInput) {
                csvFileInput.value = '';
            }
            
            if (batchResults) {
                batchResults.style.display = 'none';
            }
            
            if (csvPreview) {
                csvPreview.style.display = 'none';
            }
            
            // 重置為文字輸入標籤頁
            if (window.bookManager) {
                window.bookManager.switchBatchAddTab('text-input');
            }
        }
    }
}

// 開啟目標設定模態框
function openGoalModal() {
    const userSettings = window.dataManager ? window.dataManager.getUserSettings() : { yearlyGoal: 270 };
    const yearlyGoalInput = document.getElementById('yearlyGoal');
    
    if (yearlyGoalInput) {
        yearlyGoalInput.value = userSettings.yearlyGoal;
    }
    
    showModal('goalModal');
}

// 開啟學生管理模態框處理器
function openStudentsModalHandler() {
    console.log('開啟學生管理模態框');
    
    const studentsGrid = document.getElementById('studentsGrid');
    if (!studentsGrid) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '找不到 studentsGrid 元素',
                element: 'studentsGrid',
                context: { action: 'openStudentsModalHandler' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('找不到 studentsGrid 元素');
        }
        return;
    }

    try {
        if (window.studentManager) {
            window.studentManager.renderStudentsGrid(studentsGrid);
        }
        showModal('studentsModal');
        console.log('學生管理模態框已開啟');
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '開啟學生管理模態框時發生錯誤: ' + error.message,
                context: { action: 'openStudentsModalHandler', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('開啟學生管理模態框時發生錯誤:', error);
        }
    }
}

// 處理目標設定提交
function handleGoalSubmit(e) {
    e.preventDefault();

    const yearlyGoal = parseInt(document.getElementById('yearlyGoal').value);
    if (yearlyGoal > 0 && window.dataManager) {
        const userSettings = window.dataManager.updateUserSettings({ yearlyGoal });
        
        // 更新顯示
        const goalTarget = document.getElementById('goalTarget');
        const avgPerStudent = document.getElementById('avgPerStudent');
        
        if (goalTarget) {
            goalTarget.textContent = yearlyGoal;
        }
        
        if (avgPerStudent) {
            avgPerStudent.textContent = Math.round(yearlyGoal / 27);
        }
        
        // 更新統計
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
        
        closeModal('goalModal');
    }
}

// 鍵盤快捷鍵處理
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N: 新增書籍
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            if (window.bookManager) {
                window.bookManager.openBookModal();
            }
        }

        // Ctrl/Cmd + F: 聚焦搜尋框
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // ESC: 關閉模態框
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

// 自動儲存提醒
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(() => {
        // 每5分鐘自動提醒備份
        const books = window.dataManager ? window.dataManager.getBooks() : [];
        if (books.length > 0) {
            console.log('建議定期備份您的閱讀資料');
        }
    }, 5 * 60 * 1000);
}

// 頁面卸載前提醒
function setupBeforeUnload() {
    window.addEventListener('beforeunload', function(e) {
        const books = window.dataManager ? window.dataManager.getBooks() : [];
        if (books.length > 0) {
            // 最後一次儲存
            if (window.dataManager) {
                window.dataManager.saveBooks();
            }
        }
    });
}

// 添加匯入/匯出按鈕到頁面（可選）
function addImportExportButtons() {
    const headerActions = document.querySelector('.header-actions');
    if (!headerActions) return;

    // 匯出按鈕
    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn btn-secondary';
    exportBtn.innerHTML = '<i class="fas fa-download"></i> 匯出';
    exportBtn.onclick = () => {
        if (window.dataManager) {
            window.dataManager.exportData();
        }
    };

    // 匯入按鈕
    const importBtn = document.createElement('button');
    importBtn.className = 'btn btn-secondary';
    importBtn.innerHTML = '<i class="fas fa-upload"></i> 匯入';

    const importInput = document.createElement('input');
    importInput.type = 'file';
    importInput.accept = '.json';
    importInput.style.display = 'none';
    importInput.onchange = (e) => {
        if (window.dataManager) {
            window.dataManager.importData(e);
        }
    };

    importBtn.onclick = () => importInput.click();

    headerActions.appendChild(exportBtn);
    headerActions.appendChild(importBtn);
    headerActions.appendChild(importInput);
}

// 初始化匯入匯出功能
function initImportExport() {
    // 根據需要啟用匯入匯出功能
    // addImportExportButtons();
}

// 開啟學生管理模態框
function openStudentsModal() {
    // 觸發事件或直接調用處理器
    const event = new Event('openStudentsModal');
    window.dispatchEvent(event);
}

// 導出函數供其他模組使用
window.modalManager = {
    initModalManager,
    showModal,
    closeModal,
    openGoalModal,
    openStudentsModal,
    handleGoalSubmit,
    setupKeyboardShortcuts,
    startAutoSave,
    setupBeforeUnload,
    initImportExport
};
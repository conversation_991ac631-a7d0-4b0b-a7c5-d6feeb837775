<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>學生選擇器測試</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .test-section h3 {
            color: #495057;
            margin-top: 0;
        }
        
        select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            margin-bottom: 1rem;
            transition: border-color 0.3s ease;
        }
        
        select:focus {
            border-color: #ff9a9e;
            outline: none;
        }
        
        button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        
        .result {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b9d;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 學生選擇器功能測試</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="studentCount">0</div>
                <div class="stat-label">學生總數</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="selector1Options">0</div>
                <div class="stat-label">選擇器1選項</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="selector2Options">0</div>
                <div class="stat-label">選擇器2選項</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎓 學生選擇器測試</h3>
            
            <label for="studentSelect">頂部學生選擇器：</label>
            <select id="studentSelect" onchange="onStudentChange(this, 'selector1')">
                <option value="">選擇學生</option>
            </select>
            
            <label for="studentId">表單學生選擇器：</label>
            <select id="studentId" onchange="onStudentChange(this, 'selector2')">
                <option value="">請選擇學生</option>
            </select>
            
            <button onclick="runTest()">🔍 執行完整測試</button>
            <button onclick="loadStudents()">📚 載入學生資料</button>
            <button onclick="updateSelectors()">🔄 更新選擇器</button>
            <button onclick="clearResults()">🗑️ 清除結果</button>
        </div>
        
        <div class="test-section">
            <h3>📊 測試結果</h3>
            <div id="testResults" class="result">點擊「執行完整測試」開始測試...</div>
        </div>
    </div>

    <script>
        // 學生資料
        const defaultStudents = [
            { id: '01', name: '孔子', fullName: '孔丘' },
            { id: '02', name: '老子', fullName: '李耳' },
            { id: '03', name: '孟子', fullName: '孟軻' },
            { id: '04', name: '莊子', fullName: '莊周' },
            { id: '05', name: '荀子', fullName: '荀況' },
            { id: '06', name: '墨子', fullName: '墨翟' },
            { id: '07', name: '韓非子', fullName: '韓非' },
            { id: '08', name: '司馬遷', fullName: '司馬遷' },
            { id: '09', name: '諸葛亮', fullName: '諸葛亮' },
            { id: '10', name: '李白', fullName: '李太白' }
        ];
        
        let students = [];
        let elements = {};
        
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            results.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function updateStats() {
            document.getElementById('studentCount').textContent = students.length;
            document.getElementById('selector1Options').textContent = elements.studentSelect ? elements.studentSelect.options.length : 0;
            document.getElementById('selector2Options').textContent = elements.studentId ? elements.studentId.options.length : 0;
        }
        
        function loadStudents() {
            log('載入學生資料...', 'info');
            
            // 嘗試從 localStorage 載入，否則使用預設資料
            const stored = localStorage.getItem('classReadingTracker_students');
            if (stored) {
                students = JSON.parse(stored);
                log(`從 localStorage 載入 ${students.length} 位學生`, 'success');
            } else {
                students = [...defaultStudents];
                log(`使用預設資料，載入 ${students.length} 位學生`, 'info');
            }
            
            updateStats();
        }
        
        function initElements() {
            log('初始化 DOM 元素...', 'info');
            
            elements = {
                studentSelect: document.getElementById('studentSelect'),
                studentId: document.getElementById('studentId')
            };
            
            log(`studentSelect: ${elements.studentSelect ? '✅ 找到' : '❌ 未找到'}`, 
                elements.studentSelect ? 'success' : 'error');
            log(`studentId: ${elements.studentId ? '✅ 找到' : '❌ 未找到'}`, 
                elements.studentId ? 'success' : 'error');
        }
        
        function updateSelectors() {
            log('更新學生選擇器...', 'info');
            
            if (!elements.studentSelect || !elements.studentId) {
                log('❌ 選擇器元素未找到', 'error');
                return;
            }
            
            // 清空現有選項
            elements.studentSelect.innerHTML = '<option value="">🎓 選擇學生</option>';
            elements.studentId.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
            
            // 添加學生選項
            students.forEach(student => {
                const option1 = document.createElement('option');
                option1.value = student.id;
                option1.textContent = `${student.id}號 ${student.name}`;
                elements.studentSelect.appendChild(option1);
                
                const option2 = document.createElement('option');
                option2.value = student.id;
                option2.textContent = `${student.id}號 ${student.name}（${student.fullName}）`;
                elements.studentId.appendChild(option2);
            });
            
            log(`✅ 選擇器更新完成，共 ${students.length} 位學生`, 'success');
            updateStats();
        }
        
        function onStudentChange(select, selectorName) {
            const selectedValue = select.value;
            const selectedText = select.options[select.selectedIndex].text;
            log(`${selectorName} 選擇了: ${selectedValue} - ${selectedText}`, 'success');
        }
        
        function runTest() {
            log('=== 開始執行完整測試 ===', 'info');
            
            // 1. 初始化元素
            initElements();
            
            // 2. 載入學生資料
            loadStudents();
            
            // 3. 更新選擇器
            updateSelectors();
            
            // 4. 驗證結果
            log('=== 驗證結果 ===', 'info');
            
            if (students.length > 0) {
                log(`✅ 學生資料載入成功: ${students.length} 位學生`, 'success');
            } else {
                log('❌ 學生資料載入失敗', 'error');
            }
            
            if (elements.studentSelect && elements.studentSelect.options.length > 1) {
                log(`✅ 頂部選擇器正常: ${elements.studentSelect.options.length} 個選項`, 'success');
            } else {
                log('❌ 頂部選擇器異常', 'error');
            }
            
            if (elements.studentId && elements.studentId.options.length > 1) {
                log(`✅ 表單選擇器正常: ${elements.studentId.options.length} 個選項`, 'success');
            } else {
                log('❌ 表單選擇器異常', 'error');
            }
            
            log('=== 測試完成 ===', 'info');
            log('請嘗試點擊選擇器下拉選單，選擇不同的學生', 'info');
        }
        
        // 頁面載入時自動執行測試
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runTest();
            }, 100);
        });
    </script>
</body>
</html>

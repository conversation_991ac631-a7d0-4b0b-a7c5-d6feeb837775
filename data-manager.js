// 數據管理模組 - 負責所有數據存儲和檢索操作

// 全局變數
let books = [];
let userSettings = {
    yearlyGoal: 270
};

// 初始化數據管理器
function initDataManager() {
    console.log('初始化數據管理器...');
    loadBooks();
    loadSettings();
}

// 載入書籍數據
function loadBooks() {
    try {
        const stored = localStorage.getItem('classReadingTracker_books');
        if (stored) {
            books = JSON.parse(stored);
            console.log('從 localStorage 載入書籍資料:', books.length, '本書');
        } else {
            books = [];
            console.log('沒有找到書籍資料，使用空陣列');
        }
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '載入書籍資料時發生錯誤: ' + error.message,
                context: { action: 'loadBooks', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('載入書籍資料時發生錯誤:', error);
        }
        books = [];
    }
}

// 載入用戶設置
function loadSettings() {
    try {
        const stored = localStorage.getItem('classReadingTracker_settings');
        if (stored) {
            userSettings = { ...userSettings, ...JSON.parse(stored) };
            console.log('從 localStorage 載入用戶設置:', userSettings);
        } else {
            console.log('沒有找到用戶設置，使用預設值');
        }
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '載入用戶設置時發生錯誤: ' + error.message,
                context: { action: 'loadSettings', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('載入用戶設置時發生錯誤:', error);
        }
        userSettings = { yearlyGoal: 270 };
    }
}

// 儲存書籍數據
function saveBooks() {
    try {
        localStorage.setItem('classReadingTracker_books', JSON.stringify(books));
        console.log('書籍資料已儲存到 localStorage');
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '儲存書籍資料時發生錯誤: ' + error.message,
                context: { action: 'saveBooks', booksCount: books.length, error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('儲存書籍資料時發生錯誤:', error);
            alert('儲存失敗，請檢查瀏覽器存儲空間');
        }
    }
}

// 儲存用戶設置
function saveSettings() {
    try {
        localStorage.setItem('classReadingTracker_settings', JSON.stringify(userSettings));
        console.log('用戶設置已儲存到 localStorage');
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '儲存用戶設置時發生錯誤: ' + error.message,
                context: { action: 'saveSettings', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('儲存用戶設置時發生錯誤:', error);
            alert('儲存失敗，請檢查瀏覽器存儲空間');
        }
    }
}

// 獲取所有書籍
function getBooks() {
    return books;
}

// 獲取單本書籍
function getBook(bookId) {
    return books.find(book => book.id === bookId);
}

// 添加書籍
function addBook(book) {
    book.id = generateId();
    book.dateAdded = new Date().toISOString();
    book.dateModified = new Date().toISOString();
    books.push(book);
    saveBooks();
    return book;
}

// 更新書籍
function updateBook(bookId, updatedBook) {
    const index = books.findIndex(book => book.id === bookId);
    if (index !== -1) {
        books[index] = { ...books[index], ...updatedBook, dateModified: new Date().toISOString() };
        saveBooks();
        return books[index];
    }
    return null;
}

// 刪除書籍
function deleteBook(bookId) {
    const index = books.findIndex(book => book.id === bookId);
    if (index !== -1) {
        const deletedBook = books.splice(index, 1)[0];
        saveBooks();
        return deletedBook;
    }
    return null;
}

// 獲取用戶設置
function getUserSettings() {
    return userSettings;
}

// 更新用戶設置
function updateUserSettings(newSettings) {
    userSettings = { ...userSettings, ...newSettings };
    saveSettings();
    return userSettings;
}

// 生成唯一 ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 匯出數據
function exportData() {
    const data = {
        books: books,
        settings: userSettings,
        exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `reading-tracker-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

// 匯入數據
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);

            if (data.books && Array.isArray(data.books)) {
                if (confirm('匯入資料將覆蓋現有資料，確定要繼續嗎？')) {
                    books = data.books;
                    if (data.settings) {
                        userSettings = { ...userSettings, ...data.settings };
                        saveSettings();
                    }

                    saveBooks();
                    
                    // 觸發事件通知其他模組數據已更新
                    window.dispatchEvent(new CustomEvent('dataImported', { detail: { books, userSettings } }));
                    
                    alert('資料匯入成功！');
                }
            } else {
                alert('無效的備份檔案格式');
            }
        } catch (error) {
            alert('檔案讀取失敗，請確認檔案格式正確');
        }
    };
    reader.readAsText(file);
}

// 導出函數供其他模組使用
window.dataManager = {
    initDataManager,
    getBooks,
    getBook,
    addBook,
    updateBook,
    deleteBook,
    getUserSettings,
    updateUserSettings,
    exportData,
    importData
};
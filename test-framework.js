// 統一測試框架 - 整合所有測試功能

// 測試框架配置
const testConfig = {
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'
    autoRun: false,
    showStats: true,
    maxLogEntries: 100
};

// 測試結果統計
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    startTime: null,
    endTime: null
};

// 測試日誌
let testLogs = [];

// 測試套件
const testSuites = {
    'student-selector': {
        name: '學生選擇器測試',
        description: '測試學生選擇器的載入、顯示和交互功能',
        tests: []
    },
    'data-manager': {
        name: '數據管理器測試',
        description: '測試數據的儲存、載入和操作功能',
        tests: []
    },
    'ui-elements': {
        name: 'UI元素測試',
        description: '測試UI元素的載入、顯示和交互功能',
        tests: []
    },
    'modal-functionality': {
        name: '模態框功能測試',
        description: '測試模態框的開啟、關閉和內容顯示功能',
        tests: []
    },
    'book-management': {
        name: '書籍管理測試',
        description: '測試書籍的增刪改查功能',
        tests: []
    }
};

// 初始化測試框架
function initTestFramework() {
    console.log('初始化測試框架...');
    
    // 註冊測試
    registerStudentSelectorTests();
    registerDataManagerTests();
    registerUIElementsTests();
    registerModalFunctionalityTests();
    registerBookManagementTests();
    
    // 創建測試界面
    createTestInterface();
    
    // 自動運行測試
    if (testConfig.autoRun) {
        setTimeout(() => {
            runAllTests();
        }, 500);
    }
    
    console.log('測試框架初始化完成');
}

// 註冊學生選擇器測試
function registerStudentSelectorTests() {
    const suite = testSuites['student-selector'];
    
    suite.tests.push({
        name: '學生資料載入測試',
        description: '測試學生資料是否能正確從localStorage載入',
        run: testStudentDataLoading
    });
    
    suite.tests.push({
        name: '學生選擇器元素檢查',
        description: '檢查學生選擇器DOM元素是否存在',
        run: testStudentSelectorElements
    });
    
    suite.tests.push({
        name: '學生選擇器更新測試',
        description: '測試學生選擇器是否能正確更新選項',
        run: testStudentSelectorUpdate
    });
    
    suite.tests.push({
        name: '學生選擇交互測試',
        description: '測試學生選擇器的交互功能',
        run: testStudentSelectorInteraction
    });
}

// 註冊數據管理器測試
function registerDataManagerTests() {
    const suite = testSuites['data-manager'];
    
    suite.tests.push({
        name: '書籍數據載入測試',
        description: '測試書籍數據是否能正確從localStorage載入',
        run: testBookDataLoading
    });
    
    suite.tests.push({
        name: '書籍數據儲存測試',
        description: '測試書籍數據是否能正確儲存到localStorage',
        run: testBookDataSaving
    });
    
    suite.tests.push({
        name: '用戶設置載入測試',
        description: '測試用戶設置是否能正確載入',
        run: testUserSettingsLoading
    });
    
    suite.tests.push({
        name: '數據導出測試',
        description: '測試數據導出功能',
        run: testDataExport
    });
}

// 註冊UI元素測試
function registerUIElementsTests() {
    const suite = testSuites['ui-elements'];
    
    suite.tests.push({
        name: '關鍵UI元素檢查',
        description: '檢查關鍵UI元素是否存在',
        run: testKeyUIElements
    });
    
    suite.tests.push({
        name: '按鈕事件綁定測試',
        description: '測試按鈕事件是否正確綁定',
        run: testButtonEventBinding
    });
    
    suite.tests.push({
        name: '表單元素測試',
        description: '測試表單元素的功能',
        run: testFormElements
    });
}

// 註冊模態框功能測試
function registerModalFunctionalityTests() {
    const suite = testSuites['modal-functionality'];
    
    suite.tests.push({
        name: '模態框開啟測試',
        description: '測試模態框是否能正確開啟',
        run: testModalOpen
    });
    
    suite.tests.push({
        name: '模態框關閉測試',
        description: '測試模態框是否能正確關閉',
        run: testModalClose
    });
    
    suite.tests.push({
        name: '模態框內容測試',
        description: '測試模態框內容是否正確顯示',
        run: testModalContent
    });
}

// 註冊書籍管理測試
function registerBookManagementTests() {
    const suite = testSuites['book-management'];
    
    suite.tests.push({
        name: '書籍新增測試',
        description: '測試書籍新增功能',
        run: testBookAdd
    });
    
    suite.tests.push({
        name: '書籍編輯測試',
        description: '測試書籍編輯功能',
        run: testBookEdit
    });
    
    suite.tests.push({
        name: '書籍刪除測試',
        description: '測試書籍刪除功能',
        run: testBookDelete
    });
    
    suite.tests.push({
        name: '書籍渲染測試',
        description: '測試書籍列表渲染功能',
        run: testBookRendering
    });
}

// 學生選擇器測試函數
function testStudentDataLoading() {
    logTest('開始測試學生資料載入...');
    
    try {
        const students = window.studentManager ? window.studentManager.getStudents() : [];
        
        if (students.length === 0) {
            throw new Error('學生資料為空');
        }
        
        logTest(`成功載入 ${students.length} 位學生`, 'success');
        logTest(`前3位學生: ${students.slice(0, 3).map(s => `${s.id}號 ${s.name}`).join(', ')}`, 'info');
        
        return true;
    } catch (error) {
        logTest(`學生資料載入失敗: ${error.message}`, 'error');
        return false;
    }
}

function testStudentSelectorElements() {
    logTest('開始檢查學生選擇器元素...');
    
    try {
        const studentSelect = document.getElementById('studentSelect');
        const studentId = document.getElementById('studentId');
        
        if (!studentSelect) {
            throw new Error('找不到 studentSelect 元素');
        }
        
        if (!studentId) {
            throw new Error('找不到 studentId 元素');
        }
        
        logTest('學生選擇器元素檢查通過', 'success');
        return true;
    } catch (error) {
        logTest(`學生選擇器元素檢查失敗: ${error.message}`, 'error');
        return false;
    }
}

function testStudentSelectorUpdate() {
    logTest('開始測試學生選擇器更新...');
    
    try {
        const studentSelect = document.getElementById('studentSelect');
        const studentId = document.getElementById('studentId');
        
        if (!studentSelect || !studentId) {
            throw new Error('學生選擇器元素不存在');
        }
        
        const initialOptions1 = studentSelect.options.length;
        const initialOptions2 = studentId.options.length;
        
        // 更新選擇器
        if (window.uiManager) {
            window.uiManager.updateStudentSelectors();
        } else {
            throw new Error('UI管理器未載入');
        }
        
        const updatedOptions1 = studentSelect.options.length;
        const updatedOptions2 = studentId.options.length;
        
        if (updatedOptions1 <= 1 || updatedOptions2 <= 1) {
            throw new Error('選擇器選項數量不正確');
        }
        
        logTest(`學生選擇器更新成功: ${updatedOptions1-1} 位學生`, 'success');
        return true;
    } catch (error) {
        logTest(`學生選擇器更新失敗: ${error.message}`, 'error');
        return false;
    }
}

function testStudentSelectorInteraction() {
    logTest('開始測試學生選擇器交互...');
    
    try {
        const studentSelect = document.getElementById('studentSelect');
        
        if (!studentSelect) {
            throw new Error('學生選擇器元素不存在');
        }
        
        if (studentSelect.options.length <= 1) {
            throw new Error('學生選擇器沒有選項');
        }
        
        // 模擬選擇第二個選項
        const secondOption = studentSelect.options[1];
        studentSelect.value = secondOption.value;
        
        // 觸發change事件
        const event = new Event('change', { bubbles: true });
        studentSelect.dispatchEvent(event);
        
        logTest(`學生選擇器交互測試成功: 選擇了 ${secondOption.textContent}`, 'success');
        return true;
    } catch (error) {
        logTest(`學生選擇器交互測試失敗: ${error.message}`, 'error');
        return false;
    }
}

// 數據管理器測試函數
function testBookDataLoading() {
    logTest('開始測試書籍數據載入...');
    
    try {
        const books = window.dataManager ? window.dataManager.getBooks() : [];
        
        logTest(`成功載入 ${books.length} 本書籍`, 'success');
        return true;
    } catch (error) {
        logTest(`書籍數據載入失敗: ${error.message}`, 'error');
        return false;
    }
}

function testBookDataSaving() {
    logTest('開始測試書籍數據儲存...');
    
    try {
        if (!window.dataManager) {
            throw new Error('數據管理器未載入');
        }
        
        // 創建測試書籍
        const testBook = {
            id: 'test-' + Date.now(),
            studentId: '01',
            title: '測試書籍',
            author: '測試作者',
            status: 'to-read',
            dateAdded: new Date().toISOString()
        };
        
        // 儲存書籍
        window.dataManager.addBook(testBook);
        
        // 檢查是否儲存成功
        const books = window.dataManager.getBooks();
        const savedBook = books.find(book => book.id === testBook.id);
        
        if (!savedBook) {
            throw new Error('書籍儲存失敗');
        }
        
        // 清理測試數據
        window.dataManager.deleteBook(testBook.id);
        
        logTest('書籍數據儲存測試成功', 'success');
        return true;
    } catch (error) {
        logTest(`書籍數據儲存測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testUserSettingsLoading() {
    logTest('開始測試用戶設置載入...');
    
    try {
        const userSettings = window.dataManager ? window.dataManager.getUserSettings() : {};
        
        if (!userSettings.yearlyGoal) {
            throw new Error('年度目標設置不存在');
        }
        
        logTest(`用戶設置載入成功: 年度目標 ${userSettings.yearlyGoal}`, 'success');
        return true;
    } catch (error) {
        logTest(`用戶設置載入失敗: ${error.message}`, 'error');
        return false;
    }
}

function testDataExport() {
    logTest('開始測試數據導出...');
    
    try {
        if (!window.dataManager) {
            throw new Error('數據管理器未載入');
        }
        
        // 檢查導出函數是否存在
        if (typeof window.dataManager.exportData !== 'function') {
            throw new Error('導出函數不存在');
        }
        
        logTest('數據導出函數檢查通過', 'success');
        return true;
    } catch (error) {
        logTest(`數據導出測試失敗: ${error.message}`, 'error');
        return false;
    }
}

// UI元素測試函數
function testKeyUIElements() {
    logTest('開始檢查關鍵UI元素...');
    
    try {
        const keyElements = [
            'addBookBtn', 'setGoalBtn', 'manageStudentsBtn',
            'booksContainer', 'bookModal', 'goalModal'
        ];
        
        const missingElements = [];
        
        keyElements.forEach(id => {
            const element = document.getElementById(id);
            if (!element) {
                missingElements.push(id);
            }
        });
        
        if (missingElements.length > 0) {
            throw new Error(`缺少元素: ${missingElements.join(', ')}`);
        }
        
        logTest('關鍵UI元素檢查通過', 'success');
        return true;
    } catch (error) {
        logTest(`關鍵UI元素檢查失敗: ${error.message}`, 'error');
        return false;
    }
}

function testButtonEventBinding() {
    logTest('開始測試按鈕事件綁定...');
    
    try {
        const addBookBtn = document.getElementById('addBookBtn');
        
        if (!addBookBtn) {
            throw new Error('找不到 addBookBtn 元素');
        }
        
        // 檢查是否有事件監聽器
        const hasListener = addBookBtn.onclick || addBookBtn.addEventListener;
        
        if (!hasListener) {
            throw new Error('按鈕沒有事件監聽器');
        }
        
        logTest('按鈕事件綁定測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`按鈕事件綁定測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testFormElements() {
    logTest('開始測試表單元素...');
    
    try {
        const bookForm = document.getElementById('bookForm');
        
        if (!bookForm) {
            throw new Error('找不到 bookForm 元素');
        }
        
        // 檢查表單提交事件
        const hasSubmitListener = bookForm.onsubmit || bookForm.addEventListener;
        
        if (!hasSubmitListener) {
            throw new Error('表單沒有提交事件監聽器');
        }
        
        logTest('表單元素測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`表單元素測試失敗: ${error.message}`, 'error');
        return false;
    }
}

// 模態框功能測試函數
function testModalOpen() {
    logTest('開始測試模態框開啟...');
    
    try {
        if (!window.modalManager) {
            throw new Error('模態框管理器未載入');
        }
        
        const bookModal = document.getElementById('bookModal');
        
        if (!bookModal) {
            throw new Error('找不到 bookModal 元素');
        }
        
        // 檢查模態框是否關閉
        const isClosed = !bookModal.classList.contains('show');
        
        if (!isClosed) {
            throw new Error('模態框初始狀態應為關閉');
        }
        
        logTest('模態框開啟測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`模態框開啟測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testModalClose() {
    logTest('開始測試模態框關閉...');
    
    try {
        if (!window.modalManager) {
            throw new Error('模態框管理器未載入');
        }
        
        const closeModal = document.getElementById('closeModal');
        
        if (!closeModal) {
            throw new Error('找不到 closeModal 元素');
        }
        
        // 檢查關閉按鈕是否有事件監聽器
        const hasListener = closeModal.onclick || closeModal.addEventListener;
        
        if (!hasListener) {
            throw new Error('關閉按鈕沒有事件監聽器');
        }
        
        logTest('模態框關閉測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`模態框關閉測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testModalContent() {
    logTest('開始測試模態框內容...');
    
    try {
        const bookModalContent = document.querySelector('#bookModal .modal-content');
        
        if (!bookModalContent) {
            throw new Error('找不到模態框內容元素');
        }
        
        // 檢查模態框內容結構
        const hasHeader = bookModalContent.querySelector('.modal-header');
        const hasForm = bookModalContent.querySelector('#bookForm');
        
        if (!hasHeader || !hasForm) {
            throw new Error('模態框內容結構不完整');
        }
        
        logTest('模態框內容測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`模態框內容測試失敗: ${error.message}`, 'error');
        return false;
    }
}

// 書籍管理測試函數
function testBookAdd() {
    logTest('開始測試書籍新增...');
    
    try {
        if (!window.bookManager) {
            throw new Error('書籍管理器未載入');
        }
        
        // 檢查新增書籍函數是否存在
        if (typeof window.bookManager.openBookModal !== 'function') {
            throw new Error('新增書籍函數不存在');
        }
        
        logTest('書籍新增測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`書籍新增測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testBookEdit() {
    logTest('開始測試書籍編輯...');
    
    try {
        if (!window.bookManager) {
            throw new Error('書籍管理器未載入');
        }
        
        // 檢查編輯書籍函數是否存在
        if (typeof window.bookManager.openBookModal !== 'function') {
            throw new Error('編輯書籍函數不存在');
        }
        
        logTest('書籍編輯測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`書籍編輯測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testBookDelete() {
    logTest('開始測試書籍刪除...');
    
    try {
        if (!window.bookManager) {
            throw new Error('書籍管理器未載入');
        }
        
        // 檢查刪除書籍函數是否存在
        if (typeof window.bookManager.deleteBook !== 'function') {
            throw new Error('刪除書籍函數不存在');
        }
        
        logTest('書籍刪除測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`書籍刪除測試失敗: ${error.message}`, 'error');
        return false;
    }
}

function testBookRendering() {
    logTest('開始測試書籍渲染...');
    
    try {
        if (!window.bookManager) {
            throw new Error('書籍管理器未載入');
        }
        
        // 檢查渲染書籍函數是否存在
        if (typeof window.bookManager.renderBooks !== 'function') {
            throw new Error('渲染書籍函數不存在');
        }
        
        const booksContainer = document.getElementById('booksContainer');
        
        if (!booksContainer) {
            throw new Error('找不到 booksContainer 元素');
        }
        
        logTest('書籍渲染測試通過', 'success');
        return true;
    } catch (error) {
        logTest(`書籍渲染測試失敗: ${error.message}`, 'error');
        return false;
    }
}

// 測試日誌函數
function logTest(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
        timestamp,
        message,
        type,
        level: testConfig.logLevel
    };
    
    testLogs.push(logEntry);
    
    // 限制日誌條目數量
    if (testLogs.length > testConfig.maxLogEntries) {
        testLogs.shift();
    }
    
    // 更新測試界面
    updateTestInterface(logEntry);
    
    // 同時輸出到控制台
    switch (type) {
        case 'error':
            console.error(`[${timestamp}] ${message}`);
            break;
        case 'warn':
            console.warn(`[${timestamp}] ${message}`);
            break;
        case 'success':
            console.log(`%c[${timestamp}] ${message}`, 'color: green');
            break;
        default:
            console.log(`[${timestamp}] ${message}`);
    }
}

// 運行所有測試
function runAllTests() {
    logTest('=== 開始運行所有測試 ===', 'info');
    
    // 重置統計
    testStats = {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        startTime: new Date(),
        endTime: null
    };
    
    // 清空日誌
    testLogs = [];
    
    // 運行每個測試套件
    for (const [suiteKey, suite] of Object.entries(testSuites)) {
        logTest(`=== 運行測試套件: ${suite.name} ===`, 'info');
        
        for (const test of suite.tests) {
            testStats.total++;
            
            try {
                const result = test.run();
                if (result) {
                    testStats.passed++;
                    logTest(`✅ ${test.name}`, 'success');
                } else {
                    testStats.failed++;
                    logTest(`❌ ${test.name}`, 'error');
                }
            } catch (error) {
                testStats.failed++;
                logTest(`❌ ${test.name}: ${error.message}`, 'error');
            }
        }
    }
    
    testStats.endTime = new Date();
    
    // 顯示測試結果摘要
    showTestSummary();
    
    logTest('=== 所有測試運行完成 ===', 'info');
}

// 顯示測試結果摘要
function showTestSummary() {
    const duration = testStats.endTime - testStats.startTime;
    const durationSeconds = (duration / 1000).toFixed(2);
    
    logTest('=== 測試結果摘要 ===', 'info');
    logTest(`總測試數: ${testStats.total}`, 'info');
    logTest(`通過: ${testStats.passed}`, 'success');
    logTest(`失敗: ${testStats.failed}`, 'error');
    logTest(`跳過: ${testStats.skipped}`, 'warn');
    logTest(`成功率: ${((testStats.passed / testStats.total) * 100).toFixed(1)}%`, 'info');
    logTest(`耗時: ${durationSeconds} 秒`, 'info');
    
    // 更新統計顯示
    updateTestStats();
}

// 創建測試界面
function createTestInterface() {
    // 檢查是否已經存在測試界面
    if (document.getElementById('test-interface')) {
        return;
    }
    
    const testInterface = document.createElement('div');
    testInterface.id = 'test-interface';
    testInterface.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        width: 400px;
        max-height: 80vh;
        background: white;
        border: 2px solid #007bff;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        z-index: 10000;
        font-family: 'Noto Sans TC', sans-serif;
        display: flex;
        flex-direction: column;
    `;
    
    testInterface.innerHTML = `
        <div style="padding: 10px; background: #007bff; color: white; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 16px;">測試框架</h3>
            <button onclick="document.getElementById('test-interface').remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
        </div>
        
        <div style="padding: 10px; border-bottom: 1px solid #e9ecef;">
            <button onclick="window.testFramework.runAllTests()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 5px; margin-right: 5px; cursor: pointer;">運行所有測試</button>
            <button onclick="window.testFramework.clearLogs()" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">清除日誌</button>
        </div>
        
        <div id="test-stats" style="padding: 10px; border-bottom: 1px solid #e9ecef; font-size: 14px;">
            <div>總測試數: <span id="stat-total">0</span></div>
            <div>通過: <span id="stat-passed" style="color: green;">0</span></div>
            <div>失敗: <span id="stat-failed" style="color: red;">0</span></div>
            <div>成功率: <span id="stat-success-rate">0%</span></div>
        </div>
        
        <div id="test-logs" style="flex: 1; overflow-y: auto; padding: 10px; font-family: monospace; font-size: 12px; background: #f8f9fa;">
            <div>等待測試...</div>
        </div>
    `;
    
    document.body.appendChild(testInterface);
}

// 更新測試界面
function updateTestInterface(logEntry) {
    const logsContainer = document.getElementById('test-logs');
    if (!logsContainer) return;
    
    const logDiv = document.createElement('div');
    logDiv.style.marginBottom = '5px';
    
    switch (logEntry.type) {
        case 'error':
            logDiv.style.color = 'red';
            break;
        case 'warn':
            logDiv.style.color = 'orange';
            break;
        case 'success':
            logDiv.style.color = 'green';
            break;
        default:
            logDiv.style.color = 'black';
    }
    
    logDiv.textContent = `[${logEntry.timestamp}] ${logEntry.message}`;
    logsContainer.appendChild(logDiv);
    logsContainer.scrollTop = logsContainer.scrollHeight;
}

// 更新測試統計
function updateTestStats() {
    const statTotal = document.getElementById('stat-total');
    const statPassed = document.getElementById('stat-passed');
    const statFailed = document.getElementById('stat-failed');
    const statSuccessRate = document.getElementById('stat-success-rate');
    
    if (statTotal) statTotal.textContent = testStats.total;
    if (statPassed) statPassed.textContent = testStats.passed;
    if (statFailed) statFailed.textContent = testStats.failed;
    if (statSuccessRate) {
        const rate = testStats.total > 0 ? ((testStats.passed / testStats.total) * 100).toFixed(1) : 0;
        statSuccessRate.textContent = rate + '%';
    }
}

// 清除日誌
function clearLogs() {
    testLogs = [];
    const logsContainer = document.getElementById('test-logs');
    if (logsContainer) {
        logsContainer.innerHTML = '<div>日誌已清除...</div>';
    }
}

// 導出函數供外部使用
window.testFramework = {
    initTestFramework,
    runAllTests,
    clearLogs,
    testSuites,
    testStats,
    testLogs
};

// 當DOM載入完成後初始化測試框架
document.addEventListener('DOMContentLoaded', function() {
    // 延遲初始化，確保所有模組都已載入
    setTimeout(() => {
        initTestFramework();
    }, 1000);
});
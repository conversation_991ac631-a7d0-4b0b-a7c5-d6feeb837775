<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>統一測試框架 - 班級閱讀進度追蹤系統</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }
        
        .test-section h3 {
            color: #495057;
            margin-top: 0;
            margin-bottom: 1rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .test-card {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #ff9a9e;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.2);
        }
        
        .test-card h4 {
            color: #ff6b9d;
            margin-top: 0;
            margin-bottom: 0.5rem;
        }
        
        .test-card p {
            color: #6c757d;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .test-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .status-running {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        
        .success-btn {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        
        .danger-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b9d;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 3px;
        }
        
        .log-info {
            color: #007bff;
        }
        
        .log-success {
            color: #28a745;
            background: #d4edda;
        }
        
        .log-warn {
            color: #ffc107;
            background: #fff3cd;
        }
        
        .log-error {
            color: #dc3545;
            background: #f8d7da;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transition: width 0.3s ease;
        }
        
        .legacy-tests {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e9ecef;
        }
        
        .legacy-tests h3 {
            color: #6c757d;
        }
        
        .legacy-test-links {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .legacy-test-links a {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .legacy-test-links a:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 統一測試框架 - 班級閱讀進度追蹤系統</h1>
        
        <div class="test-section">
            <h3>📊 測試統計</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="total-tests">0</div>
                    <div class="stat-label">總測試數</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="passed-tests">0</div>
                    <div class="stat-label">通過</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="failed-tests">0</div>
                    <div class="stat-label">失敗</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="test-progress" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 測試控制</h3>
            <div class="controls">
                <button class="primary-btn" onclick="runAllTests()">🚀 運行所有測試</button>
                <button onclick="runSpecificSuite('student-selector')">👥 學生選擇器測試</button>
                <button onclick="runSpecificSuite('data-manager')">💾 數據管理器測試</button>
                <button onclick="runSpecificSuite('ui-elements')">🎨 UI元素測試</button>
                <button onclick="runSpecificSuite('modal-functionality')">🪟 模態框測試</button>
                <button onclick="runSpecificSuite('book-management')">📚 書籍管理測試</button>
                <button class="danger-btn" onclick="clearAllResults()">🗑️ 清除結果</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 測試套件</h3>
            <div class="test-grid" id="test-suites">
                <!-- 測試套件卡片將在這裡動態生成 -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 測試日誌</h3>
            <div class="controls">
                <button onclick="clearLogs()">清除日誌</button>
                <button onclick="exportLogs()">導出日誌</button>
                <button onclick="toggleAutoScroll()">自動滾動: <span id="auto-scroll-status">開啟</span></button>
            </div>
            <div class="log-container" id="test-logs">
                <div class="log-entry log-info">等待測試開始...</div>
            </div>
        </div>
        
        <div class="test-section legacy-tests">
            <h3>🔍 舊版測試頁面</h3>
            <p>以下為原始的測試頁面，保留用於對比和特殊情況測試：</p>
            <div class="legacy-test-links">
                <a href="test.html" target="_blank">學生名單測試</a>
                <a href="test-student-selector.html" target="_blank">學生選擇器測試</a>
                <a href="debug-test.html" target="_blank">調試測試</a>
                <a href="minimal-test.html" target="_blank">最小化測試</a>
                <a href="button-test.html" target="_blank">按鈕功能測試</a>
            </div>
        </div>
    </div>

    <script>
        // 測試狀態
        let testState = {
            isRunning: false,
            autoScroll: true,
            currentSuite: null,
            results: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0
            }
        };
        
        // 測試套件配置
        const testSuites = {
            'student-selector': {
                name: '學生選擇器測試',
                description: '測試學生選擇器的載入、顯示和交互功能',
                icon: '👥',
                tests: [
                    { name: '學生資料載入測試', run: testStudentDataLoading },
                    { name: '學生選擇器元素檢查', run: testStudentSelectorElements },
                    { name: '學生選擇器更新測試', run: testStudentSelectorUpdate },
                    { name: '學生選擇交互測試', run: testStudentSelectorInteraction }
                ]
            },
            'data-manager': {
                name: '數據管理器測試',
                description: '測試數據的儲存、載入和操作功能',
                icon: '💾',
                tests: [
                    { name: '書籍數據載入測試', run: testBookDataLoading },
                    { name: '書籍數據儲存測試', run: testBookDataSaving },
                    { name: '用戶設置載入測試', run: testUserSettingsLoading },
                    { name: '數據導出測試', run: testDataExport }
                ]
            },
            'ui-elements': {
                name: 'UI元素測試',
                description: '測試UI元素的載入、顯示和交互功能',
                icon: '🎨',
                tests: [
                    { name: '關鍵UI元素檢查', run: testKeyUIElements },
                    { name: '按鈕事件綁定測試', run: testButtonEventBinding },
                    { name: '表單元素測試', run: testFormElements }
                ]
            },
            'modal-functionality': {
                name: '模態框功能測試',
                description: '測試模態框的開啟、關閉和內容顯示功能',
                icon: '🪟',
                tests: [
                    { name: '模態框開啟測試', run: testModalOpen },
                    { name: '模態框關閉測試', run: testModalClose },
                    { name: '模態框內容測試', run: testModalContent }
                ]
            },
            'book-management': {
                name: '書籍管理測試',
                description: '測試書籍的增刪改查功能',
                icon: '📚',
                tests: [
                    { name: '書籍新增測試', run: testBookAdd },
                    { name: '書籍編輯測試', run: testBookEdit },
                    { name: '書籍刪除測試', run: testBookDelete },
                    { name: '書籍渲染測試', run: testBookRendering }
                ]
            }
        };
        
        // 初始化測試頁面
        function initTestPage() {
            renderTestSuites();
            updateStats();
            log('測試頁面初始化完成', 'info');
        }
        
        // 渲染測試套件
        function renderTestSuites() {
            const container = document.getElementById('test-suites');
            container.innerHTML = '';
            
            for (const [key, suite] of Object.entries(testSuites)) {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.innerHTML = `
                    <div class="test-status">
                        <h4>${suite.icon} ${suite.name}</h4>
                        <span class="status-badge status-pending" id="status-${key}">待測試</span>
                    </div>
                    <p>${suite.description}</p>
                    <div>
                        <small>測試數: ${suite.tests.length}</small>
                    </div>
                `;
                container.appendChild(card);
            }
        }
        
        // 運行所有測試
        async function runAllTests() {
            if (testState.isRunning) {
                log('測試正在運行中，請稍候...', 'warn');
                return;
            }
            
            testState.isRunning = true;
            testState.results = { total: 0, passed: 0, failed: 0, skipped: 0 };
            
            log('=== 開始運行所有測試 ===', 'info');
            
            // 禁用所有按鈕
            disableAllButtons(true);
            
            for (const [key, suite] of Object.entries(testSuites)) {
                await runTestSuite(key, suite);
            }
            
            testState.isRunning = false;
            disableAllButtons(false);
            
            log('=== 所有測試運行完成 ===', 'info');
            showTestSummary();
        }
        
        // 運行特定測試套件
        async function runSpecificSuite(suiteKey) {
            if (testState.isRunning) {
                log('測試正在運行中，請稍候...', 'warn');
                return;
            }
            
            const suite = testSuites[suiteKey];
            if (!suite) {
                log(`找不到測試套件: ${suiteKey}`, 'error');
                return;
            }
            
            testState.isRunning = true;
            testState.currentSuite = suiteKey;
            
            log(`=== 開始運行測試套件: ${suite.name} ===`, 'info');
            
            // 禁用所有按鈕
            disableAllButtons(true);
            
            await runTestSuite(suiteKey, suite);
            
            testState.isRunning = false;
            testState.currentSuite = null;
            disableAllButtons(false);
            
            log(`=== 測試套件 ${suite.name} 運行完成 ===`, 'info');
            updateStats();
        }
        
        // 運行測試套件
        async function runTestSuite(suiteKey, suite) {
            updateSuiteStatus(suiteKey, 'running');
            
            for (const test of suite.tests) {
                testState.results.total++;
                
                try {
                    log(`運行測試: ${test.name}`, 'info');
                    
                    // 添加延遲以避免阻塞UI
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    const result = test.run();
                    
                    if (result) {
                        testState.results.passed++;
                        log(`✅ ${test.name}`, 'success');
                    } else {
                        testState.results.failed++;
                        log(`❌ ${test.name}`, 'error');
                    }
                } catch (error) {
                    testState.results.failed++;
                    log(`❌ ${test.name}: ${error.message}`, 'error');
                }
                
                updateStats();
            }
            
            const suitePassed = suite.tests.every(test => {
                // 這裡可以添加更複雜的邏輯來判斷測試是否通過
                return true; // 簡化處理
            });
            
            updateSuiteStatus(suiteKey, suitePassed ? 'passed' : 'failed');
        }
        
        // 更新測試套件狀態
        function updateSuiteStatus(suiteKey, status) {
            const statusElement = document.getElementById(`status-${suiteKey}`);
            if (statusElement) {
                statusElement.className = `status-badge status-${status}`;
                switch (status) {
                    case 'running':
                        statusElement.textContent = '運行中';
                        break;
                    case 'passed':
                        statusElement.textContent = '通過';
                        break;
                    case 'failed':
                        statusElement.textContent = '失敗';
                        break;
                    default:
                        statusElement.textContent = '待測試';
                }
            }
        }
        
        // 更新統計信息
        function updateStats() {
            document.getElementById('total-tests').textContent = testState.results.total;
            document.getElementById('passed-tests').textContent = testState.results.passed;
            document.getElementById('failed-tests').textContent = testState.results.failed;
            
            const successRate = testState.results.total > 0 
                ? Math.round((testState.results.passed / testState.results.total) * 100) 
                : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            // 更新進度條
            const progress = testState.results.total > 0 
                ? (testState.results.passed / testState.results.total) * 100 
                : 0;
            document.getElementById('test-progress').style.width = progress + '%';
        }
        
        // 顯示測試摘要
        function showTestSummary() {
            const successRate = testState.results.total > 0 
                ? Math.round((testState.results.passed / testState.results.total) * 100) 
                : 0;
            
            log('=== 測試結果摘要 ===', 'info');
            log(`總測試數: ${testState.results.total}`, 'info');
            log(`通過: ${testState.results.passed}`, 'success');
            log(`失敗: ${testState.results.failed}`, 'error');
            log(`成功率: ${successRate}%`, successRate >= 80 ? 'success' : 'warn');
        }
        
        // 日誌功能
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('test-logs');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logsContainer.appendChild(logEntry);
            
            if (testState.autoScroll) {
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        }
        
        // 清除日誌
        function clearLogs() {
            const logsContainer = document.getElementById('test-logs');
            logsContainer.innerHTML = '<div class="log-entry log-info">日誌已清除...</div>';
        }
        
        // 導出日誌
        function exportLogs() {
            const logsContainer = document.getElementById('test-logs');
            const logText = logsContainer.innerText;
            
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            log('日誌已導出', 'success');
        }
        
        // 切換自動滾動
        function toggleAutoScroll() {
            testState.autoScroll = !testState.autoScroll;
            document.getElementById('auto-scroll-status').textContent = testState.autoScroll ? '開啟' : '關閉';
        }
        
        // 清除所有結果
        function clearAllResults() {
            testState.results = { total: 0, passed: 0, failed: 0, skipped: 0 };
            updateStats();
            clearLogs();
            
            // 重置所有測試套件狀態
            for (const key of Object.keys(testSuites)) {
                updateSuiteStatus(key, 'pending');
            }
            
            // 重置進度條
            document.getElementById('test-progress').style.width = '0%';
            
            log('所有測試結果已清除', 'info');
        }
        
        // 禁用/啟用所有按鈕
        function disableAllButtons(disabled) {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.disabled = disabled;
            });
        }
        
        // 測試函數實現
        function testStudentDataLoading() {
            try {
                const students = window.studentManager ? window.studentManager.getStudents() : [];
                return students.length > 0;
            } catch (error) {
                log(`學生資料載入失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testStudentSelectorElements() {
            try {
                const studentSelect = document.getElementById('studentSelect');
                const studentId = document.getElementById('studentId');
                return studentSelect && studentId;
            } catch (error) {
                log(`學生選擇器元素檢查失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testStudentSelectorUpdate() {
            try {
                const studentSelect = document.getElementById('studentSelect');
                if (!studentSelect) return false;
                
                const initialOptions = studentSelect.options.length;
                
                if (window.uiManager) {
                    window.uiManager.updateStudentSelectors();
                }
                
                const updatedOptions = studentSelect.options.length;
                return updatedOptions > 1;
            } catch (error) {
                log(`學生選擇器更新失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testStudentSelectorInteraction() {
            try {
                const studentSelect = document.getElementById('studentSelect');
                if (!studentSelect || studentSelect.options.length <= 1) return false;
                
                const secondOption = studentSelect.options[1];
                studentSelect.value = secondOption.value;
                
                const event = new Event('change', { bubbles: true });
                studentSelect.dispatchEvent(event);
                
                return true;
            } catch (error) {
                log(`學生選擇器交互測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookDataLoading() {
            try {
                const books = window.dataManager ? window.dataManager.getBooks() : [];
                return true; // 總是返回true，因為書籍可能為空
            } catch (error) {
                log(`書籍數據載入失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookDataSaving() {
            try {
                if (!window.dataManager) return false;
                
                const testBook = {
                    id: 'test-' + Date.now(),
                    studentId: '01',
                    title: '測試書籍',
                    author: '測試作者',
                    status: 'to-read',
                    dateAdded: new Date().toISOString()
                };
                
                window.dataManager.addBook(testBook);
                
                const books = window.dataManager.getBooks();
                const savedBook = books.find(book => book.id === testBook.id);
                
                if (savedBook) {
                    window.dataManager.deleteBook(testBook.id);
                    return true;
                }
                
                return false;
            } catch (error) {
                log(`書籍數據儲存測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testUserSettingsLoading() {
            try {
                const userSettings = window.dataManager ? window.dataManager.getUserSettings() : {};
                return !!userSettings.yearlyGoal;
            } catch (error) {
                log(`用戶設置載入失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testDataExport() {
            try {
                return window.dataManager && typeof window.dataManager.exportData === 'function';
            } catch (error) {
                log(`數據導出測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testKeyUIElements() {
            try {
                const keyElements = [
                    'addBookBtn', 'setGoalBtn', 'manageStudentsBtn',
                    'booksContainer', 'bookModal', 'goalModal'
                ];
                
                return keyElements.every(id => document.getElementById(id));
            } catch (error) {
                log(`關鍵UI元素檢查失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testButtonEventBinding() {
            try {
                const addBookBtn = document.getElementById('addBookBtn');
                return addBookBtn && (addBookBtn.onclick || addBookBtn.addEventListener);
            } catch (error) {
                log(`按鈕事件綁定測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testFormElements() {
            try {
                const bookForm = document.getElementById('bookForm');
                return bookForm && (bookForm.onsubmit || bookForm.addEventListener);
            } catch (error) {
                log(`表單元素測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testModalOpen() {
            try {
                const bookModal = document.getElementById('bookModal');
                return bookModal && !bookModal.classList.contains('show');
            } catch (error) {
                log(`模態框開啟測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testModalClose() {
            try {
                const closeModal = document.getElementById('closeModal');
                return closeModal && (closeModal.onclick || closeModal.addEventListener);
            } catch (error) {
                log(`模態框關閉測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testModalContent() {
            try {
                const bookModalContent = document.querySelector('#bookModal .modal-content');
                if (!bookModalContent) return false;
                
                const hasHeader = bookModalContent.querySelector('.modal-header');
                const hasForm = bookModalContent.querySelector('#bookForm');
                
                return hasHeader && hasForm;
            } catch (error) {
                log(`模態框內容測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookAdd() {
            try {
                return window.bookManager && typeof window.bookManager.openBookModal === 'function';
            } catch (error) {
                log(`書籍新增測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookEdit() {
            try {
                return window.bookManager && typeof window.bookManager.openBookModal === 'function';
            } catch (error) {
                log(`書籍編輯測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookDelete() {
            try {
                return window.bookManager && typeof window.bookManager.deleteBook === 'function';
            } catch (error) {
                log(`書籍刪除測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        function testBookRendering() {
            try {
                const booksContainer = document.getElementById('booksContainer');
                return window.bookManager && 
                       typeof window.bookManager.renderBooks === 'function' && 
                       booksContainer;
            } catch (error) {
                log(`書籍渲染測試失敗: ${error.message}`, 'error');
                return false;
            }
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待一秒確保所有模組都已載入
            setTimeout(() => {
                initTestPage();
            }, 1000);
        });
    </script>
</body>
</html>
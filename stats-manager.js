// 統計管理模組 - 負責統計數據的計算和顯示

// 初始化統計管理器
function initStatsManager() {
    console.log('初始化統計管理器...');
    try {
        updateStats();
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UNKNOWN,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '初始化統計管理器時發生錯誤: ' + error.message,
                context: { action: 'initStatsManager', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('初始化統計管理器時發生錯誤:', error);
        }
    }
}

// 更新統計資料
function updateStats() {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const students = window.studentManager ? window.studentManager.getStudents() : [];
    const userSettings = window.dataManager ? window.dataManager.getUserSettings() : { yearlyGoal: 270 };

    const totalBooks = books.length;
    const completedBooks = books.filter(book => book.status === 'completed').length;
    const readingBooks = books.filter(book => book.status === 'reading').length;
    const totalPages = books.reduce((sum, book) => sum + (book.currentPage || 0), 0);

    // 計算活躍學生數（有閱讀記錄的學生）
    const activeStudentIds = [...new Set(books.map(book => book.studentId))];
    const activeStudents = activeStudentIds.length;

    // 更新顯示
    updateStatDisplay('totalBooks', totalBooks);
    updateStatDisplay('completedBooks', completedBooks);
    updateStatDisplay('readingBooks', readingBooks);
    updateStatDisplay('totalPages', totalPages.toLocaleString());
    updateStatDisplay('activeStudents', activeStudents);

    // 更新目標進度
    updateGoalProgress(completedBooks, userSettings.yearlyGoal);
}

// 更新單個統計數據顯示
function updateStatDisplay(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    } else {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.LOW,
                message: `找不到統計元素: ${elementId}`,
                element: elementId,
                context: { action: 'updateStatDisplay', value },
                timestamp: new Date().toISOString()
            });
        } else {
            console.warn(`找不到統計元素: ${elementId}`);
        }
    }
}

// 更新目標進度
function updateGoalProgress(completedBooks, yearlyGoal) {
    const goalProgress = document.getElementById('goalProgress');
    const goalProgressBar = document.getElementById('goalProgressBar');
    
    if (goalProgress) {
        goalProgress.textContent = completedBooks;
    }
    
    if (goalProgressBar) {
        const goalPercentage = Math.min((completedBooks / yearlyGoal) * 100, 100);
        goalProgressBar.style.width = `${goalPercentage}%`;
    }
}

// 獲取班級閱讀統計
function getClassReadingStats() {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const students = window.studentManager ? window.studentManager.getStudents() : [];
    
    const stats = {
        totalBooks: books.length,
        completedBooks: books.filter(book => book.status === 'completed').length,
        readingBooks: books.filter(book => book.status === 'reading').length,
        toReadBooks: books.filter(book => book.status === 'to-read').length,
        abandonedBooks: books.filter(book => book.status === 'abandoned').length,
        totalPages: books.reduce((sum, book) => sum + (book.currentPage || 0), 0),
        totalBookPages: books.reduce((sum, book) => sum + (book.totalPages || 0), 0),
        activeStudents: [...new Set(books.map(book => book.studentId))].length,
        totalStudents: students.length,
        averageBooksPerStudent: 0,
        completionRate: 0,
        averageRating: 0
    };

    // 計算平均每人書籍數
    if (stats.activeStudents > 0) {
        stats.averageBooksPerStudent = Math.round((stats.totalBooks / stats.activeStudents) * 10) / 10;
    }

    // 計算完成率
    if (stats.totalBooks > 0) {
        stats.completionRate = Math.round((stats.completedBooks / stats.totalBooks) * 100);
    }

    // 計算平均評分
    const ratedBooks = books.filter(book => book.rating && book.rating > 0);
    if (ratedBooks.length > 0) {
        const totalRating = ratedBooks.reduce((sum, book) => sum + book.rating, 0);
        stats.averageRating = Math.round((totalRating / ratedBooks.length) * 10) / 10;
    }

    return stats;
}

// 獲取學生閱讀統計
function getStudentReadingStats(studentId) {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const studentBooks = books.filter(book => book.studentId === studentId);
    
    if (studentBooks.length === 0) {
        return {
            studentId,
            totalBooks: 0,
            completedBooks: 0,
            readingBooks: 0,
            toReadBooks: 0,
            abandonedBooks: 0,
            totalPages: 0,
            totalBookPages: 0,
            averageRating: 0,
            favoriteGenre: '',
            readingStreak: 0
        };
    }

    const stats = {
        studentId,
        totalBooks: studentBooks.length,
        completedBooks: studentBooks.filter(book => book.status === 'completed').length,
        readingBooks: studentBooks.filter(book => book.status === 'reading').length,
        toReadBooks: studentBooks.filter(book => book.status === 'to-read').length,
        abandonedBooks: studentBooks.filter(book => book.status === 'abandoned').length,
        totalPages: studentBooks.reduce((sum, book) => sum + (book.currentPage || 0), 0),
        totalBookPages: studentBooks.reduce((sum, book) => sum + (book.totalPages || 0), 0),
        averageRating: 0,
        favoriteGenre: '',
        readingStreak: 0
    };

    // 計算平均評分
    const ratedBooks = studentBooks.filter(book => book.rating && book.rating > 0);
    if (ratedBooks.length > 0) {
        const totalRating = ratedBooks.reduce((sum, book) => sum + book.rating, 0);
        stats.averageRating = Math.round((totalRating / ratedBooks.length) * 10) / 10;
    }

    // 計算最喜歡的類型
    const genres = {};
    studentBooks.forEach(book => {
        if (book.tags && book.tags.length > 0) {
            book.tags.forEach(tag => {
                genres[tag] = (genres[tag] || 0) + 1;
            });
        }
    });
    
    const favoriteGenre = Object.keys(genres).reduce((a, b) => genres[a] > genres[b] ? a : b, '');
    stats.favoriteGenre = favoriteGenre;

    return stats;
}

// 獲取閱讀趨勢數據
function getReadingTrends(days = 30) {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const now = new Date();
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    const recentBooks = books.filter(book => {
        const bookDate = new Date(book.dateAdded || book.dateModified);
        return bookDate >= startDate;
    });
    
    const trends = {
        period: days,
        totalBooksAdded: recentBooks.length,
        booksCompleted: recentBooks.filter(book => book.status === 'completed').length,
        averageBooksPerDay: Math.round((recentBooks.length / days) * 10) / 10,
        dailyData: []
    };
    
    // 生成每日數據
    for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
        
        const dayBooks = recentBooks.filter(book => {
            const bookDate = new Date(book.dateAdded || book.dateModified);
            return bookDate >= dayStart && bookDate < dayEnd;
        });
        
        trends.dailyData.push({
            date: dayStart.toISOString().split('T')[0],
            booksAdded: dayBooks.length,
            booksCompleted: dayBooks.filter(book => book.status === 'completed').length
        });
    }
    
    return trends;
}

// 生成統計報告
function generateStatsReport() {
    const classStats = getClassReadingStats();
    const studentStats = window.studentManager ? window.studentManager.getStudentStats() : [];
    const trends = getReadingTrends(30);
    
    let report = `📊 班級閱讀進度統計報告 📊\n\n`;
    report += `📈 基本統計:\n`;
    report += `  • 總書籍數: ${classStats.totalBooks} 本\n`;
    report += `  • 已完成: ${classStats.completedBooks} 本\n`;
    report += `  • 閱讀中: ${classStats.readingBooks} 本\n`;
    report += `  • 待讀: ${classStats.toReadBooks} 本\n`;
    report += `  • 暫停: ${classStats.abandonedBooks} 本\n`;
    report += `  • 總頁數: ${classStats.totalPages.toLocaleString()} 頁\n`;
    report += `  • 活躍學生: ${classStats.activeStudents} / ${classStats.totalStudents} 人\n`;
    report += `  • 平均每人: ${classStats.averageBooksPerStudent} 本\n`;
    report += `  • 完成率: ${classStats.completionRate}%\n`;
    report += `  • 平均評分: ${classStats.averageRating} / 5\n\n`;
    
    report += `📖 最近30天趨勢:\n`;
    report += `  • 新增書籍: ${trends.totalBooksAdded} 本\n`;
    report += `  • 完成書籍: ${trends.booksCompleted} 本\n`;
    report += `  • 平均每日: ${trends.averageBooksPerDay} 本\n\n`;
    
    if (studentStats.length > 0) {
        report += `🏆 學生表現排行:\n`;
        const topStudents = studentStats
            .sort((a, b) => b.completedBooks - a.completedBooks)
            .slice(0, 5);
        
        topStudents.forEach((student, index) => {
            report += `  ${index + 1}. ${student.studentName}: ${student.completedBooks} 本完成\n`;
        });
    }
    
    return report;
}

// 顯示統計報告
function showStatsReport() {
    const report = generateStatsReport();
    
    // 創建模態框顯示報告
    const modalDiv = document.createElement('div');
    modalDiv.className = 'modal';
    modalDiv.id = 'statsReportModal';
    modalDiv.style.display = 'flex';
    modalDiv.innerHTML = `
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h2>統計報告</h2>
                <button class="close-btn" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="stats-report-content">
                <pre>${report}</pre>
            </div>
            <div class="form-actions">
                <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">關閉</button>
                <button class="btn btn-primary" onclick="window.statsManager.downloadStatsReport()">下載報告</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modalDiv);
    document.body.style.overflow = 'hidden';
    
    // 點擊模態框外部關閉
    modalDiv.addEventListener('click', (e) => {
        if (e.target === modalDiv) {
            modalDiv.remove();
            document.body.style.overflow = 'auto';
        }
    });
}

// 下載統計報告
function downloadStatsReport() {
    const report = generateStatsReport();
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `閱讀統計報告-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // 關閉模態框
    const modal = document.getElementById('statsReportModal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// 導出函數供其他模組使用
window.statsManager = {
    initStatsManager,
    updateStats,
    getClassReadingStats,
    getStudentReadingStats,
    getReadingTrends,
    generateStatsReport,
    showStatsReport,
    downloadStatsReport
};
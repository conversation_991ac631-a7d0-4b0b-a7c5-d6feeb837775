/* 基礎樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans TC', sans-serif;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
    min-height: 100vh;
    color: #333;
    position: relative;
}

/* 可愛的背景裝飾 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 頂部導航 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-bottom: 3px solid rgba(255, 182, 193, 0.3);
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(255, 182, 193, 0.2);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ff6b9d;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-shadow: 2px 2px 4px rgba(255, 107, 157, 0.2);
}

.app-title i {
    color: #ff9a9e;
    font-size: 2.2rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.font-size-selector select {
    padding: 0.875rem 1.25rem;
    border: 3px solid #ffb6c1;
    border-radius: 25px;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.9);
    color: #ff6b9d;
    min-width: 120px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
}

.font-size-selector select:focus {
    outline: none;
    border-color: #ff9a9e;
    box-shadow: 0 0 0 4px rgba(255, 154, 158, 0.2), 0 4px 20px rgba(255, 182, 193, 0.3);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

.font-size-selector select option {
    padding: 0.5rem;
    color: #ff6b9d;
    background: white;
}

.student-selector select {
    padding: 0.875rem 1.25rem;
    border: 3px solid #ffb6c1;
    border-radius: 25px;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.9);
    color: #ff6b9d;
    min-width: 180px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
}

.student-selector select:focus {
    outline: none;
    border-color: #ff9a9e;
    box-shadow: 0 0 0 4px rgba(255, 154, 158, 0.2), 0 4px 20px rgba(255, 182, 193, 0.3);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

.student-selector select option {
    padding: 0.5rem;
    color: #ff6b9d;
    background: white;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: #a0aec0;
}

.search-box input {
    padding: 0.875rem 1.25rem 0.875rem 3rem;
    border: 3px solid #ffb6c1;
    border-radius: 30px;
    font-size: 0.95rem;
    width: 320px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
}

.search-box input:focus {
    outline: none;
    border-color: #ff9a9e;
    box-shadow: 0 0 0 4px rgba(255, 154, 158, 0.2), 0 4px 20px rgba(255, 182, 193, 0.3);
    transform: translateY(-2px);
}

.search-box input::placeholder {
    color: #ff9a9e;
    opacity: 0.7;
}

/* 按鈕樣式 */
.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
    background: linear-gradient(135deg, #ff8a95 0%, #fec5ec 50%, #fec5ec 100%);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #ff6b9d;
    border: 2px solid #ffb6c1;
}

.btn-secondary:hover {
    background: rgba(255, 182, 193, 0.2);
    border-color: #ff9a9e;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 182, 193, 0.3);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* 主內容區 */
.main-content {
    flex: 1;
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

/* 側邊欄 */
.sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stats-panel,
.filter-panel,
.goal-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
    border: 2px solid rgba(255, 182, 193, 0.3);
    position: relative;
    overflow: hidden;
}

.stats-panel::before,
.filter-panel::before,
.goal-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #ff9a9e 100%);
}

.stats-panel h3,
.filter-panel h3,
.goal-panel h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ff6b9d;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
}

.stats-panel h3::after,
.filter-panel h3::after,
.goal-panel h3::after {
    content: '✨';
    margin-left: 0.5rem;
    font-size: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: rgba(255, 182, 193, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 182, 193, 0.2);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 182, 193, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    color: #ff6b9d;
    font-size: 0.95rem;
    font-weight: 500;
}

.stat-value {
    font-weight: 700;
    color: #ff4757;
    font-size: 1.3rem;
    text-shadow: 1px 1px 2px rgba(255, 71, 87, 0.2);
}

/* 篩選按鈕 */
.filter-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.875rem 1.25rem;
    border: 2px solid rgba(255, 182, 193, 0.3);
    background: rgba(255, 255, 255, 0.8);
    color: #ff6b9d;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 0.95rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn:hover {
    background: rgba(255, 182, 193, 0.2);
    color: #ff4757;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
}

.filter-btn.active {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-color: #ff9a9e;
    box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
}

/* 學生篩選 */
.student-filter {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.student-filter h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.goal-subtitle {
    font-size: 0.8rem;
    color: #64748b;
    text-align: center;
    margin-top: 0.5rem;
}

/* 目標進度 */
.goal-progress {
    margin-bottom: 1rem;
}

.goal-text {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ff9a9e 100%);
    border-radius: 6px;
    transition: width 0.5s ease;
    width: 0%;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 主內容區 */
.content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    background: white;
    color: #64748b;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    border-color: #cbd5e0;
    color: #475569;
}

.view-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.sort-controls select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    cursor: pointer;
}

.sort-controls select:focus {
    outline: none;
    border-color: #667eea;
}

/* 書籍容器 */
.books-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.books-container.list-view {
    grid-template-columns: 1fr;
}

/* 書籍卡片 */
.book-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.book-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(255, 182, 193, 0.3);
    border-color: #ff9a9e;
}

.book-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ff9a9e 100%);
}

.book-card::after {
    content: '📚';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
    transition: all 0.3s ease;
}

.book-card:hover::after {
    opacity: 0.6;
    transform: rotate(10deg) scale(1.1);
}

.book-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.book-info h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ff4757;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    text-shadow: 1px 1px 2px rgba(255, 71, 87, 0.1);
}

.book-info .author {
    color: #ff6b9d;
    font-size: 0.95rem;
    font-weight: 500;
}

.book-info .student-name {
    color: #ff9a9e;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.book-info .student-number {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    padding: 0.3rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 700;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.book-status {
    padding: 0.4rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.book-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.book-status:hover::before {
    left: 100%;
}

.status-to-read {
    background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
    color: #2d5016;
}

.status-reading {
    background: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%);
    color: #8b0000;
}

.status-completed {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
}

.status-abandoned {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    color: #2d3436;
}

/* 表單中的小提示文字 */
form small {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #64748b;
}

/* 學生管理樣式 */
.students-management {
    padding: 1.5rem;
}

.students-header {
    margin-bottom: 2rem;
}

.students-header p {
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.students-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    max-height: 60vh;
    overflow-y: auto;
}

.student-item {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.student-item:hover {
    border-color: #cbd5e0;
}

.student-item.modified {
    border-color: #667eea;
    background: #f0f4ff;
}

.student-number {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.student-fields {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.student-field {
    display: flex;
    flex-direction: column;
}

.student-field label {
    font-size: 0.8rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.student-field input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.student-field input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.student-field input.modified {
    border-color: #667eea;
    background: #f9fafb;
}

.book-progress {
    margin: 1rem 0;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #64748b;
}

.progress-bar-book {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill-book {
    height: 100%;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ff9a9e 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill-book::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

.book-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.book-rating {
    display: flex;
    gap: 0.2rem;
}

.book-rating i {
    color: #ffd700;
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.book-rating i:hover {
    transform: scale(1.2);
}

.book-rating i.empty {
    color: #ffb6c1;
    opacity: 0.5;
}

.book-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.6rem;
    border: 2px solid rgba(255, 182, 193, 0.3);
    background: rgba(255, 255, 255, 0.8);
    color: #ff6b9d;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(255, 182, 193, 0.2);
}

.action-btn:hover {
    background: rgba(255, 182, 193, 0.2);
    color: #ff4757;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
}

.action-btn.edit:hover {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border-color: #74b9ff;
}

.action-btn.delete:hover {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
    border-color: #fd79a8;
}

/* 標籤 */
.book-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin: 1rem 0;
}

.tag {
    padding: 0.4rem 0.8rem;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #2d3436;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(250, 177, 160, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tag::before {
    content: '#';
    margin-right: 0.2rem;
    opacity: 0.7;
}

.tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(250, 177, 160, 0.4);
}

/* 空狀態 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #ff6b9d;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 25px;
    margin: 2rem;
    border: 2px dashed #ffb6c1;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.empty-state i {
    font-size: 5rem;
    color: #ff9a9e;
    margin-bottom: 1.5rem;
    animation: bounce 2s infinite;
    position: relative;
    z-index: 1;
}

.empty-state h3 {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ff4757;
    text-shadow: 1px 1px 2px rgba(255, 71, 87, 0.2);
    position: relative;
    z-index: 1;
}

.empty-state p {
    font-size: 1.1rem;
    color: #ff6b9d;
    position: relative;
    z-index: 1;
}

/* 模態框樣式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(135deg, #fff 0%, #ffeef8 100%);
    border-radius: 25px;
    width: 100%;
    max-width: 520px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 80px rgba(255, 182, 193, 0.4);
    animation: slideUp 0.4s ease;
    border: 3px solid rgba(255, 182, 193, 0.3);
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #ff9a9e 100%);
    border-radius: 25px 25px 0 0;
}

.modal-large {
    max-width: 800px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ff6b9d;
    text-shadow: 1px 1px 2px rgba(255, 107, 157, 0.2);
}

.modal-header h2::before {
    content: '✨ ';
    margin-right: 0.5rem;
}

.close-btn {
    padding: 0.6rem;
    border: 2px solid rgba(255, 182, 193, 0.3);
    background: rgba(255, 255, 255, 0.8);
    color: #ff6b9d;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.close-btn:hover {
    background: rgba(255, 182, 193, 0.2);
    color: #ff4757;
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
}

/* 表單樣式 */
form {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #ffb6c1;
    border-radius: 15px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(255, 182, 193, 0.1);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff9a9e;
    box-shadow: 0 0 0 4px rgba(255, 154, 158, 0.2), 0 4px 15px rgba(255, 182, 193, 0.3);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* 評分輸入 */
.rating-input {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.rating-input i {
    font-size: 1.8rem;
    color: #ffb6c1;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.rating-input i:hover,
.rating-input i.active {
    color: #ffd700;
    transform: scale(1.3) rotate(10deg);
    text-shadow: 2px 2px 4px rgba(255, 215, 0, 0.3);
}

.rating-input i:hover {
    animation: starPulse 0.6s ease-in-out;
}

@keyframes starPulse {
    0%, 100% { transform: scale(1.3) rotate(10deg); }
    50% { transform: scale(1.5) rotate(15deg); }
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

/* 書籍詳情 */
.book-detail-content {
    padding: 1.5rem;
}

.detail-header {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.detail-cover {
    width: 120px;
    height: 180px;
    border-radius: 8px;
    object-fit: cover;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.detail-info h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.detail-info .author {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.detail-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.meta-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    display: block;
}

.meta-label {
    font-size: 0.8rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.detail-notes {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1.5rem;
}

.detail-notes h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.detail-notes p {
    color: #4a5568;
    line-height: 1.6;
}

/* 響應式設計 */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
        gap: 1rem;
    }

    .stats-panel,
    .filter-panel,
    .goal-panel {
        min-width: 250px;
        flex-shrink: 0;
    }

    .books-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-box input {
        width: 200px;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .books-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        flex-direction: column;
    }

    .stats-panel,
    .filter-panel,
    .goal-panel {
        min-width: auto;
    }

    .modal {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .detail-header {
        flex-direction: column;
        text-align: center;
    }

    .detail-cover {
        align-self: center;
    }
}

/* 批次新增模態框樣式 */
.batch-add-content {
    padding: 1.5rem;
}

.batch-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: #64748b;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.tab-btn:hover {
    color: #ff6b9d;
    background: rgba(255, 182, 193, 0.1);
}

.tab-btn.active {
    color: #ff6b9d;
    border-bottom-color: #ff6b9d;
    background: rgba(255, 182, 193, 0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h4 {
    color: #ff6b9d;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.tab-content p {
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.tab-content textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 2px solid #ffb6c1;
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(255, 182, 193, 0.1);
}

.tab-content textarea:focus {
    outline: none;
    border-color: #ff9a9e;
    box-shadow: 0 0 0 4px rgba(255, 154, 158, 0.2), 0 4px 15px rgba(255, 182, 193, 0.3);
    background: rgba(255, 255, 255, 1);
}

.file-upload {
    margin-bottom: 1rem;
}

.file-upload input[type="file"] {
    display: none;
}

.file-upload label {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
}

.file-upload label:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 154, 158, 0.4);
}

.csv-preview {
    margin-top: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    overflow: hidden;
    background: white;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.preview-table th,
.preview-table td {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    text-align: left;
}

.preview-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.preview-table tr:nth-child(even) {
    background: #f8fafc;
}

.batch-results {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 15px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
}

.success-results {
    border-color: #a8e6cf;
    background: rgba(168, 230, 207, 0.1);
}

.success-results h5 {
    color: #2d5016;
    margin-bottom: 0.75rem;
}

.success-results ul {
    list-style: none;
    padding: 0;
}

.success-results li {
    padding: 0.5rem 0;
    color: #2d5016;
    border-bottom: 1px solid rgba(168, 230, 207, 0.3);
}

.success-results li:last-child {
    border-bottom: none;
}

.error-results {
    border-color: #fd79a8;
    background: rgba(253, 121, 168, 0.1);
}

.error-results h5 {
    color: #8b0000;
    margin-bottom: 0.75rem;
}

.error-results ul {
    list-style: none;
    padding: 0;
}

.error-results li {
    padding: 0.5rem 0;
    color: #8b0000;
    border-bottom: 1px solid rgba(253, 121, 168, 0.3);
}

.error-results li:last-child {
    border-bottom: none;
}

.error-results small {
    display: block;
    color: #64748b;
    margin-top: 0.25rem;
    font-size: 0.8rem;
}

.format-example {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    border: 1px solid #e2e8f0;
}

.format-example h5 {
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.format-example pre {
    background: white;
    padding: 0.75rem;
    border-radius: 8px;
    overflow-x: auto;
    font-size: 0.8rem;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.format-example p {
    color: #64748b;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* 字體大小調整功能 - 影響側邊欄和書籍卡片內容 */
body.font-size-small .sidebar {
    font-size: 14px;
}

body.font-size-small .sidebar h3 {
    font-size: 1rem;
}

body.font-size-small .sidebar h4 {
    font-size: 0.9rem;
}

body.font-size-small .stat-label {
    font-size: 0.8rem;
}

body.font-size-small .stat-value {
    font-size: 1rem;
}

body.font-size-small .filter-btn {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

body.font-size-small .goal-text {
    font-size: 0.9rem;
}

body.font-size-small .goal-subtitle {
    font-size: 0.8rem;
}

body.font-size-small .btn-small {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

/* 書籍卡片字體大小調整 - 小字體 */
body.font-size-small .book-info h3 {
    font-size: 1.1rem;
}

body.font-size-small .book-info .author {
    font-size: 0.85rem;
}

body.font-size-small .book-info .student-name {
    font-size: 0.85rem;
}

body.font-size-small .student-number {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
}

body.font-size-small .book-status {
    font-size: 0.7rem;
    padding: 0.3rem 0.8rem;
}

body.font-size-small .progress-text {
    font-size: 0.8rem;
}

body.font-size-small .tag {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
}

body.font-size-small .book-rating i {
    font-size: 0.85rem;
}

body.font-size-small .action-btn {
    font-size: 0.8rem;
    padding: 0.5rem;
}

body.font-size-medium .sidebar {
    font-size: 16px;
}

body.font-size-medium .sidebar h3 {
    font-size: 1.2rem;
}

body.font-size-medium .sidebar h4 {
    font-size: 1.1rem;
}

body.font-size-medium .stat-label {
    font-size: 1rem;
}

body.font-size-medium .stat-value {
    font-size: 1.3rem;
}

body.font-size-medium .filter-btn {
    font-size: 0.95rem;
    padding: 0.6rem 1.2rem;
}

body.font-size-medium .goal-text {
    font-size: 1rem;
}

body.font-size-medium .goal-subtitle {
    font-size: 0.9rem;
}

body.font-size-medium .btn-small {
    font-size: 0.95rem;
    padding: 0.6rem 1.2rem;
}

/* 書籍卡片字體大小調整 - 中字體 */
body.font-size-medium .book-info h3 {
    font-size: 1.3rem;
}

body.font-size-medium .book-info .author {
    font-size: 0.95rem;
}

body.font-size-medium .book-info .student-name {
    font-size: 0.9rem;
}

body.font-size-medium .student-number {
    font-size: 0.75rem;
    padding: 0.3rem 0.75rem;
}

body.font-size-medium .book-status {
    font-size: 0.8rem;
    padding: 0.4rem 1rem;
}

body.font-size-medium .progress-text {
    font-size: 0.9rem;
}

body.font-size-medium .tag {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
}

body.font-size-medium .book-rating i {
    font-size: 1rem;
}

body.font-size-medium .action-btn {
    font-size: 0.9rem;
    padding: 0.6rem;
}

body.font-size-large .sidebar {
    font-size: 18px;
}

body.font-size-large .sidebar h3 {
    font-size: 1.4rem;
}

body.font-size-large .sidebar h4 {
    font-size: 1.3rem;
}

body.font-size-large .stat-label {
    font-size: 1.1rem;
}

body.font-size-large .stat-value {
    font-size: 1.6rem;
}

body.font-size-large .filter-btn {
    font-size: 1.1rem;
    padding: 0.7rem 1.4rem;
}

body.font-size-large .goal-text {
    font-size: 1.1rem;
}

body.font-size-large .goal-subtitle {
    font-size: 1rem;
}

body.font-size-large .btn-small {
    font-size: 1.1rem;
    padding: 0.7rem 1.4rem;
}

/* 書籍卡片字體大小調整 - 大字體 */
body.font-size-large .book-info h3 {
    font-size: 1.5rem;
}

body.font-size-large .book-info .author {
    font-size: 1.05rem;
}

body.font-size-large .book-info .student-name {
    font-size: 0.95rem;
}

body.font-size-large .student-number {
    font-size: 0.8rem;
    padding: 0.35rem 0.85rem;
}

body.font-size-large .book-status {
    font-size: 0.9rem;
    padding: 0.5rem 1.2rem;
}

body.font-size-large .progress-text {
    font-size: 1rem;
}

body.font-size-large .tag {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

body.font-size-large .book-rating i {
    font-size: 1.1rem;
}

body.font-size-large .action-btn {
    font-size: 1rem;
    padding: 0.7rem;
}

/* 響應式設計中的字體大小調整 */
@media (max-width: 1024px) {
    body.font-size-small .sidebar {
        font-size: 13px;
    }
    
    body.font-size-small .book-info h3 {
        font-size: 1rem;
    }
    
    body.font-size-small .book-info .author {
        font-size: 0.8rem;
    }
    
    body.font-size-small .book-info .student-name {
        font-size: 0.8rem;
    }
    
    body.font-size-medium .sidebar {
        font-size: 15px;
    }
    
    body.font-size-medium .book-info h3 {
        font-size: 1.2rem;
    }
    
    body.font-size-medium .book-info .author {
        font-size: 0.9rem;
    }
    
    body.font-size-medium .book-info .student-name {
        font-size: 0.85rem;
    }
    
    body.font-size-large .sidebar {
        font-size: 17px;
    }
    
    body.font-size-large .book-info h3 {
        font-size: 1.4rem;
    }
    
    body.font-size-large .book-info .author {
        font-size: 1rem;
    }
    
    body.font-size-large .book-info .student-name {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    body.font-size-small .sidebar {
        font-size: 12px;
    }
    
    body.font-size-small .book-info h3 {
        font-size: 0.9rem;
    }
    
    body.font-size-small .book-info .author {
        font-size: 0.75rem;
    }
    
    body.font-size-small .book-info .student-name {
        font-size: 0.75rem;
    }
    
    body.font-size-medium .sidebar {
        font-size: 14px;
    }
    
    body.font-size-medium .book-info h3 {
        font-size: 1.1rem;
    }
    
    body.font-size-medium .book-info .author {
        font-size: 0.85rem;
    }
    
    body.font-size-medium .book-info .student-name {
        font-size: 0.8rem;
    }
    
    body.font-size-large .sidebar {
        font-size: 16px;
    }
    
    body.font-size-large .book-info h3 {
        font-size: 1.3rem;
    }
    
    body.font-size-large .book-info .author {
        font-size: 0.95rem;
    }
    
    body.font-size-large .book-info .student-name {
        font-size: 0.85rem;
    }
}

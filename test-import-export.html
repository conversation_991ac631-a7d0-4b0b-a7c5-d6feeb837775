<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>導入/導出功能測試</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: 'Noto Sans TC', sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button.primary {
            background-color: #007bff;
            color: white;
        }
        .test-button.secondary {
            background-color: #6c757d;
            color: white;
        }
        .test-button.success {
            background-color: #28a745;
            color: white;
        }
        .test-data {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .file-input {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>導入/導出功能測試</h1>
        
        <div class="test-section">
            <h3>1. 初始化測試</h3>
            <button class="test-button primary" onclick="testInitialization()">測試初始化</button>
            <div id="initialization-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 導入/導出按鈕測試</h3>
            <button class="test-button primary" onclick="testImportExportButtons()">測試按鈕添加</button>
            <div id="buttons-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 導出功能測試</h3>
            <button class="test-button success" onclick="testExportData()">測試數據導出</button>
            <div id="export-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 導入功能測試</h3>
            <div class="file-input">
                <input type="file" id="import-file" accept=".json" onchange="testImportData(event)">
                <button class="test-button secondary" onclick="document.getElementById('import-file').click()">選擇文件導入</button>
            </div>
            <div id="import-result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 測試數據生成</h3>
            <button class="test-button primary" onclick="generateTestData()">生成測試數據</button>
            <button class="test-button secondary" onclick="clearTestData()">清除測試數據</button>
            <div id="test-data-result"></div>
        </div>
        
        <div class="test-section">
            <h3>6. 錯誤處理測試</h3>
            <button class="test-button primary" onclick="testErrorHandling()">測試錯誤處理</button>
            <div id="error-handling-result"></div>
        </div>
        
        <div class="test-section">
            <h3>7. 測試日誌</h3>
            <div id="test-log" class="test-data"></div>
            <button class="test-button secondary" onclick="clearTestLog()">清除日誌</button>
        </div>
    </div>

    <script src="error-handler.js"></script>
    <script src="data-manager.js"></script>
    <script src="student-manager.js"></script>
    <script src="book-manager.js"></script>
    <script src="ui-manager.js"></script>
    <script src="modal-manager.js"></script>
    <script src="stats-manager.js"></script>
    <script src="app.js"></script>
    <script src="script.js"></script>
    
    <script>
        // 測試日誌
        let testLog = [];
        
        function logTest(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            updateTestLog();
            console.log(logEntry);
        }
        
        function updateTestLog() {
            const logElement = document.getElementById('test-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearTestLog() {
            testLog = [];
            updateTestLog();
        }
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }
        
        // 1. 初始化測試
        function testInitialization() {
            logTest('開始初始化測試');
            
            try {
                // 檢查必要的全局變數和函數
                const checks = [
                    { name: 'window.errorHandler', exists: typeof window.errorHandler !== 'undefined' },
                    { name: 'window.dataManager', exists: typeof window.dataManager !== 'undefined' },
                    { name: 'window.studentManager', exists: typeof window.studentManager !== 'undefined' },
                    { name: 'window.bookManager', exists: typeof window.bookManager !== 'undefined' },
                    { name: 'exportData', exists: typeof exportData === 'function' },
                    { name: 'importData', exists: typeof importData === 'function' },
                    { name: 'addImportExportButtons', exists: typeof addImportExportButtons === 'function' }
                ];
                
                const results = checks.map(check => `${check.name}: ${check.exists ? '✅' : '❌'}`).join('<br>');
                
                const allPassed = checks.every(check => check.exists);
                showResult('initialization-result', 
                    `初始化檢查結果：<br>${results}<br><br>整體狀態：${allPassed ? '✅ 通過' : '❌ 失敗'}`, 
                    allPassed ? 'success' : 'error');
                
                logTest(`初始化測試完成，結果：${allPassed ? '通過' : '失敗'}`, allPassed ? 'success' : 'error');
            } catch (error) {
                showResult('initialization-result', `初始化測試失敗：${error.message}`, 'error');
                logTest(`初始化測試失敗：${error.message}`, 'error');
            }
        }
        
        // 2. 導入/導出按鈕測試
        function testImportExportButtons() {
            logTest('開始導入/導出按鈕測試');
            
            try {
                // 創建測試用的 header-actions 元素
                let headerActions = document.querySelector('.header-actions');
                if (!headerActions) {
                    headerActions = document.createElement('div');
                    headerActions.className = 'header-actions';
                    headerActions.style.cssText = 'display: flex; gap: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px;';
                    document.querySelector('.test-container').insertBefore(headerActions, document.querySelector('.test-section'));
                }
                
                // 清空現有按鈕
                headerActions.innerHTML = '';
                
                // 調用添加按鈕函數
                addImportExportButtons();
                
                // 檢查按鈕是否添加成功
                const exportBtn = headerActions.querySelector('button[title="匯出閱讀數據備份"]');
                const importBtn = headerActions.querySelector('button[title="匯入閱讀數據備份"]');
                const importInput = headerActions.querySelector('input[type="file"]');
                
                const success = exportBtn && importBtn && importInput;
                
                showResult('buttons-result', 
                    `按鈕添加結果：<br>` +
                    `匯出按鈕：${exportBtn ? '✅' : '❌'}<br>` +
                    `匯入按鈕：${importBtn ? '✅' : '❌'}<br>` +
                    `文件輸入：${importInput ? '✅' : '❌'}<br><br>` +
                    `整體狀態：${success ? '✅ 通過' : '❌ 失敗'}`, 
                    success ? 'success' : 'error');
                
                logTest(`導入/導出按鈕測試完成，結果：${success ? '通過' : '失敗'}`, success ? 'success' : 'error');
            } catch (error) {
                showResult('buttons-result', `按鈕測試失敗：${error.message}`, 'error');
                logTest(`按鈕測試失敗：${error.message}`, 'error');
            }
        }
        
        // 3. 導出功能測試
        function testExportData() {
            logTest('開始導出功能測試');
            
            try {
                // 生成一些測試數據
                if (!window.dataManager || window.dataManager.getBooks().length === 0) {
                    generateTestData();
                }
                
                // 創建一個模擬的下載鏈接來捕獲導出的數據
                const originalCreateElement = document.createElement;
                let capturedBlob = null;
                
                document.createElement = function(tagName) {
                    const element = originalCreateElement.call(document, tagName);
                    if (tagName === 'a') {
                        const originalClick = element.click;
                        element.click = function() {
                            capturedBlob = element.href;
                            logTest('捕獲到導出數據');
                        };
                    }
                    return element;
                };
                
                // 執行導出
                exportData();
                
                // 恢復原始函數
                document.createElement = originalCreateElement;
                
                // 檢查結果
                const success = capturedBlob !== null;
                
                showResult('export-result', 
                    `導出功能測試結果：<br>` +
                    `數據捕獲：${success ? '✅' : '❌'}<br>` +
                    `數據大小：${success ? '已捕獲' : '未捕獲'}<br><br>` +
                    `整體狀態：${success ? '✅ 通過' : '❌ 失敗'}`, 
                    success ? 'success' : 'error');
                
                logTest(`導出功能測試完成，結果：${success ? '通過' : '失敗'}`, success ? 'success' : 'error');
            } catch (error) {
                showResult('export-result', `導出測試失敗：${error.message}`, 'error');
                logTest(`導出測試失敗：${error.message}`, 'error');
            }
        }
        
        // 4. 導入功能測試
        function testImportData(event) {
            logTest('開始導入功能測試');
            
            const file = event.target.files[0];
            if (!file) {
                showResult('import-result', '請選擇一個文件', 'info');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // 驗證數據格式
                    const isValid = data.books && Array.isArray(data.books);
                    
                    if (isValid) {
                        // 執行導入
                        importData(event);
                        
                        showResult('import-result', 
                            `導入功能測試結果：<br>` +
                            `文件格式：✅ 正確<br>` +
                            `書籍數量：${data.books.length}<br>` +
                            `學生數量：${data.students ? data.students.length : 0}<br>` +
                            `版本：${data.version || '1.0'}<br><br>` +
                            `整體狀態：✅ 通過`, 
                            'success');
                        
                        logTest(`導入功能測試完成，結果：通過，書籍數量：${data.books.length}`, 'success');
                    } else {
                        throw new Error('無效的文件格式');
                    }
                } catch (error) {
                    showResult('import-result', `導入測試失敗：${error.message}`, 'error');
                    logTest(`導入測試失敗：${error.message}`, 'error');
                }
            };
            
            reader.onerror = function() {
                showResult('import-result', '文件讀取失敗', 'error');
                logTest('文件讀取失敗', 'error');
            };
            
            reader.readAsText(file);
        }
        
        // 5. 生成測試數據
        function generateTestData() {
            logTest('開始生成測試數據');
            
            try {
                const testBooks = [
                    {
                        id: 'test-book-1',
                        studentId: '01',
                        title: '論語',
                        author: '孔子',
                        totalPages: 200,
                        currentPage: 150,
                        status: 'reading',
                        rating: 5,
                        dateAdded: new Date().toISOString(),
                        dateModified: new Date().toISOString()
                    },
                    {
                        id: 'test-book-2',
                        studentId: '02',
                        title: '道德經',
                        author: '老子',
                        totalPages: 100,
                        currentPage: 100,
                        status: 'completed',
                        rating: 5,
                        dateAdded: new Date().toISOString(),
                        dateModified: new Date().toISOString()
                    }
                ];
                
                if (window.dataManager) {
                    window.dataManager.setBooks(testBooks);
                } else {
                    localStorage.setItem('classReadingTracker_books', JSON.stringify(testBooks));
                }
                
                showResult('test-data-result', 
                    `測試數據生成成功<br>` +
                    `書籍數量：${testBooks.length}<br>` +
                    `包含書籍：${testBooks.map(b => b.title).join(', ')}`, 
                    'success');
                
                logTest(`測試數據生成成功，書籍數量：${testBooks.length}`, 'success');
            } catch (error) {
                showResult('test-data-result', `測試數據生成失敗：${error.message}`, 'error');
                logTest(`測試數據生成失敗：${error.message}`, 'error');
            }
        }
        
        function clearTestData() {
            logTest('開始清除測試數據');
            
            try {
                if (window.dataManager) {
                    window.dataManager.setBooks([]);
                } else {
                    localStorage.setItem('classReadingTracker_books', JSON.stringify([]));
                }
                
                showResult('test-data-result', '測試數據已清除', 'info');
                logTest('測試數據已清除', 'info');
            } catch (error) {
                showResult('test-data-result', `清除測試數據失敗：${error.message}`, 'error');
                logTest(`清除測試數據失敗：${error.message}`, 'error');
            }
        }
        
        // 6. 錯誤處理測試
        function testErrorHandling() {
            logTest('開始錯誤處理測試');
            
            try {
                const tests = [];
                
                // 測試 1: 錯誤處理器是否存在
                tests.push({
                    name: '錯誤處理器初始化',
                    result: typeof window.errorHandler !== 'undefined' ? '✅' : '❌'
                });
                
                // 測試 2: 錯誤類型定義
                tests.push({
                    name: '錯誤類型定義',
                    result: window.errorHandler && window.errorHandler.ErrorTypes ? '✅' : '❌'
                });
                
                // 測試 3: 錯誤嚴重級別定義
                tests.push({
                    name: '錯誤嚴重級別定義',
                    result: window.errorHandler && window.errorHandler.ErrorSeverity ? '✅' : '❌'
                });
                
                // 測試 4: 錯誤處理函數
                tests.push({
                    name: '錯誤處理函數',
                    result: window.errorHandler && typeof window.errorHandler.handleError === 'function' ? '✅' : '❌'
                });
                
                // 測試 5: 事件記錄函數
                tests.push({
                    name: '事件記錄函數',
                    result: window.errorHandler && typeof window.errorHandler.logEvent === 'function' ? '✅' : '❌'
                });
                
                const results = tests.map(test => `${test.name}：${test.result}`).join('<br>');
                const allPassed = tests.every(test => test.result === '✅');
                
                showResult('error-handling-result', 
                    `錯誤處理測試結果：<br>${results}<br><br>整體狀態：${allPassed ? '✅ 通過' : '❌ 失敗'}`, 
                    allPassed ? 'success' : 'error');
                
                logTest(`錯誤處理測試完成，結果：${allPassed ? '通過' : '失敗'}`, allPassed ? 'success' : 'error');
            } catch (error) {
                showResult('error-handling-result', `錯誤處理測試失敗：${error.message}`, 'error');
                logTest(`錯誤處理測試失敗：${error.message}`, 'error');
            }
        }
        
        // 頁面載入完成後自動執行初始化測試
        document.addEventListener('DOMContentLoaded', function() {
            logTest('測試頁面載入完成');
            testInitialization();
        });
    </script>
</body>
</html>
// 錯誤處理模組 - 提供統一的錯誤處理和用戶反饋機制

// 錯誤類型定義
const ErrorTypes = {
    NETWORK: 'network',
    STORAGE: 'storage',
    VALIDATION: 'validation',
    PERMISSION: 'permission',
    DATA_CORRUPTION: 'data_corruption',
    UI_ERROR: 'ui_error',
    UNKNOWN: 'unknown'
};

// 錯誤嚴重級別
const ErrorSeverity = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

// 錯誤配置
const errorConfig = {
    maxErrors: 100,
    autoReport: false,
    showNotifications: true,
    logToConsole: true,
    enableRecovery: true
};

// 錯誤歷史記錄
let errorHistory = [];
let errorCount = 0;

// 初始化錯誤處理器
function initErrorHandler() {
    console.log('初始化錯誤處理器...');
    
    // 設置全局錯誤處理
    setupGlobalErrorHandlers();
    
    // 創建錯誤通知容器
    createErrorNotificationContainer();
    
    // 監聽自定義錯誤事件
    setupCustomErrorListeners();
    
    console.log('錯誤處理器初始化完成');
}

// 設置全局錯誤處理
function setupGlobalErrorHandlers() {
    // 捕獲未處理的 JavaScript 錯誤
    window.addEventListener('error', function(event) {
        const error = {
            type: ErrorTypes.UNKNOWN,
            severity: ErrorSeverity.HIGH,
            message: event.message,
            filename: event.filename,
            line: event.lineno,
            column: event.colno,
            error: event.error,
            timestamp: new Date().toISOString()
        };
        
        handleError(error);
    });
    
    // 捕獲未處理的 Promise 拒絕
    window.addEventListener('unhandledrejection', function(event) {
        const error = {
            type: ErrorTypes.UNKNOWN,
            severity: ErrorSeverity.HIGH,
            message: '未處理的 Promise 拒絕: ' + (event.reason.message || event.reason),
            reason: event.reason,
            timestamp: new Date().toISOString()
        };
        
        handleError(error);
    });
    
    // 捕獲 localStorage 錯誤
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;
    
    localStorage.setItem = function(key, value) {
        try {
            return originalSetItem.call(this, key, value);
        } catch (error) {
            const storageError = {
                type: ErrorTypes.STORAGE,
                severity: ErrorSeverity.MEDIUM,
                message: 'localStorage 儲存失敗: ' + error.message,
                key: key,
                error: error,
                timestamp: new Date().toISOString()
            };
            
            handleError(storageError);
            throw error;
        }
    };
    
    localStorage.getItem = function(key) {
        try {
            return originalGetItem.call(this, key);
        } catch (error) {
            const storageError = {
                type: ErrorTypes.STORAGE,
                severity: ErrorSeverity.MEDIUM,
                message: 'localStorage 讀取失敗: ' + error.message,
                key: key,
                error: error,
                timestamp: new Date().toISOString()
            };
            
            handleError(storageError);
            throw error;
        }
    };
}

// 創建錯誤通知容器
function createErrorNotificationContainer() {
    const container = document.createElement('div');
    container.id = 'error-notification-container';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
        pointer-events: none;
    `;
    
    document.body.appendChild(container);
}

// 設置自定義錯誤監聽器
function setupCustomErrorListeners() {
    // 監聽存儲錯誤
    window.addEventListener('storageError', function(event) {
        const error = {
            type: ErrorTypes.STORAGE,
            severity: event.detail.severity || ErrorSeverity.MEDIUM,
            message: event.detail.message,
            context: event.detail.context,
            timestamp: new Date().toISOString()
        };
        
        handleError(error);
    });
    
    // 監聽驗證錯誤
    window.addEventListener('validationError', function(event) {
        const error = {
            type: ErrorTypes.VALIDATION,
            severity: ErrorSeverity.LOW,
            message: event.detail.message,
            field: event.detail.field,
            value: event.detail.value,
            timestamp: new Date().toISOString()
        };
        
        handleError(error);
    });
    
    // 監聽UI錯誤
    window.addEventListener('uiError', function(event) {
        const error = {
            type: ErrorTypes.UI_ERROR,
            severity: event.detail.severity || ErrorSeverity.MEDIUM,
            message: event.detail.message,
            element: event.detail.element,
            action: event.detail.action,
            timestamp: new Date().toISOString()
        };
        
        handleError(error);
    });
}

// 處理錯誤
function handleError(error) {
    // 記錄錯誤
    logError(error);
    
    // 顯示用戶通知
    if (errorConfig.showNotifications) {
        showErrorNotification(error);
    }
    
    // 嘗試錯誤恢復
    if (errorConfig.enableRecovery) {
        attemptErrorRecovery(error);
    }
    
    // 自動報告錯誤（如果啟用）
    if (errorConfig.autoReport) {
        reportError(error);
    }
}

// 記錄錯誤
function logError(error) {
    errorCount++;
    
    // 添加到錯誤歷史
    errorHistory.push({
        ...error,
        id: generateErrorId(),
        count: errorCount
    });
    
    // 限制錯誤歷史大小
    if (errorHistory.length > errorConfig.maxErrors) {
        errorHistory = errorHistory.slice(-errorConfig.maxErrors);
    }
    
    // 控制台日誌
    if (errorConfig.logToConsole) {
        console.group(`🚨 錯誤 #${errorCount} [${error.type.toUpperCase()}]`);
        console.error('消息:', error.message);
        console.error('時間:', error.timestamp);
        console.error('嚴重級別:', error.severity);
        
        if (error.context) {
            console.error('上下文:', error.context);
        }
        
        if (error.error) {
            console.error('錯誤對象:', error.error);
        }
        
        console.groupEnd();
    }
}

// 顯示錯誤通知
function showErrorNotification(error) {
    const container = document.getElementById('error-notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-left: 4px solid ${getErrorColor(error.severity)};
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        pointer-events: auto;
        cursor: pointer;
    `;
    
    const icon = getErrorIcon(error.type);
    const title = getErrorTitle(error.type);
    const message = formatErrorMessage(error);
    
    notification.innerHTML = `
        <div style="display: flex; align-items: flex-start;">
            <div style="font-size: 20px; margin-right: 10px; margin-top: 2px;">${icon}</div>
            <div style="flex: 1;">
                <div style="font-weight: bold; margin-bottom: 5px; color: ${getErrorColor(error.severity)};">${title}</div>
                <div style="font-size: 14px; color: #666; line-height: 1.4;">${message}</div>
                ${error.severity === ErrorSeverity.CRITICAL ? `
                    <div style="margin-top: 10px;">
                        <button onclick="window.errorHandler.showErrorDetails('${errorHistory[errorHistory.length - 1]?.id}')" 
                                style="background: ${getErrorColor(error.severity)}; color: white; border: none; padding: 5px 10px; border-radius: 5px; font-size: 12px; cursor: pointer;">
                            查看詳情
                        </button>
                    </div>
                ` : ''}
            </div>
            <div style="margin-left: 10px; font-size: 16px; color: #999; cursor: pointer;" onclick="this.parentElement.remove()">×</div>
        </div>
    `;
    
    container.appendChild(notification);
    
    // 動畫顯示
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // 自動隱藏
    const autoHideTime = error.severity === ErrorSeverity.CRITICAL ? 10000 : 
                        error.severity === ErrorSeverity.HIGH ? 8000 : 5000;
    
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, autoHideTime);
    
    // 點擊通知
    notification.addEventListener('click', function(e) {
        if (e.target.tagName !== 'BUTTON') {
            this.style.opacity = '0';
            this.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (this.parentElement) {
                    this.remove();
                }
            }, 300);
        }
    });
}

// 嘗試錯誤恢復
function attemptErrorRecovery(error) {
    switch (error.type) {
        case ErrorTypes.STORAGE:
            recoverFromStorageError(error);
            break;
            
        case ErrorTypes.VALIDATION:
            recoverFromValidationError(error);
            break;
            
        case ErrorTypes.UI_ERROR:
            recoverFromUIError(error);
            break;
            
        case ErrorTypes.DATA_CORRUPTION:
            recoverFromDataCorruption(error);
            break;
            
        default:
            // 未知錯誤類型，提供一般恢復建議
            showGeneralRecoveryAdvice(error);
    }
}

// 存儲錯誤恢復
function recoverFromStorageError(error) {
    console.log('嘗試從存儲錯誤恢復...');
    
    // 檢查是否是配額問題
    if (error.message.includes('quota') || error.message.includes('QuotaExceededError')) {
        showRecoveryNotification('存儲空間不足', '請清理瀏覽器存儲空間或刪除不必要的數據。', {
            actions: [
                { text: '清理數據', action: () => clearNonEssentialData() },
                { text: '導出數據', action: () => exportDataForBackup() }
            ]
        });
    }
    
    // 檢查是否是訪問問題
    else if (error.message.includes('access') || error.message.includes('SecurityError')) {
        showRecoveryNotification('存儲訪問受限', '請確保瀏覽器允許使用本地存儲。', {
            actions: [
                { text: '重新加載頁面', action: () => location.reload() }
            ]
        });
    }
}

// 驗證錯誤恢復
function recoverFromValidationError(error) {
    console.log('嘗試從驗證錯誤恢復...');
    
    if (error.field) {
        // 高亮顯示錯誤欄位
        const field = document.getElementById(error.field);
        if (field) {
            field.style.borderColor = '#dc3545';
            field.style.backgroundColor = '#fff5f5';
            
            // 添加錯誤提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.style.cssText = `
                color: #dc3545;
                font-size: 12px;
                margin-top: 5px;
            `;
            errorDiv.textContent = error.message;
            
            field.parentNode.appendChild(errorDiv);
            
            // 3秒後移除錯誤樣式
            setTimeout(() => {
                field.style.borderColor = '';
                field.style.backgroundColor = '';
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 3000);
        }
    }
}

// UI錯誤恢復
function recoverFromUIError(error) {
    console.log('嘗試從UI錯誤恢復...');
    
    if (error.element) {
        const element = document.getElementById(error.element);
        if (element) {
            // 重新初始化元素
            if (window.uiManager) {
                window.uiManager.initializeElements();
            }
        }
    }
}

// 數據損壞恢復
function recoverFromDataCorruption(error) {
    console.log('嘗試從數據損壞恢復...');
    
    showRecoveryNotification('數據可能已損壞', '檢測到數據可能已損壞，建議進行數據恢復。', {
        actions: [
            { text: '使用備份恢復', action: () => restoreFromBackup() },
            { text: '重置數據', action: () => resetCorruptedData() }
        ]
    });
}

// 顯示恢復通知
function showRecoveryNotification(title, message, options = {}) {
    const container = document.getElementById('error-notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = 'recovery-notification';
    notification.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-left: 4px solid #ffc107;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        pointer-events: auto;
    `;
    
    let actionsHtml = '';
    if (options.actions) {
        actionsHtml = options.actions.map(action => 
            `<button onclick="(${action.action.toString()})()" 
                     style="background: #ffc107; color: #212529; border: none; padding: 5px 10px; border-radius: 5px; font-size: 12px; cursor: pointer; margin-right: 5px; margin-top: 10px;">
                ${action.text}
             </button>`
        ).join('');
    }
    
    notification.innerHTML = `
        <div style="display: flex; align-items: flex-start;">
            <div style="font-size: 20px; margin-right: 10px; margin-top: 2px;">⚠️</div>
            <div style="flex: 1;">
                <div style="font-weight: bold; margin-bottom: 5px; color: #856404;">${title}</div>
                <div style="font-size: 14px; color: #666; line-height: 1.4;">${message}</div>
                ${actionsHtml}
            </div>
            <div style="margin-left: 10px; font-size: 16px; color: #999; cursor: pointer;" onclick="this.parentElement.remove()">×</div>
        </div>
    `;
    
    container.appendChild(notification);
    
    // 動畫顯示
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
}

// 顯示一般恢復建議
function showGeneralRecoveryAdvice(error) {
    const advice = getRecoveryAdvice(error.type);
    showRecoveryNotification('系統遇到問題', advice.message, advice.actions);
}

// 獲取恢復建議
function getRecoveryAdvice(errorType) {
    const adviceMap = {
        [ErrorTypes.NETWORK]: {
            message: '網絡連接可能出現問題，請檢查您的網絡設置。',
            actions: [
                { text: '檢查網絡', action: () => checkNetworkConnection() },
                { text: '重新加載', action: () => location.reload() }
            ]
        },
        [ErrorTypes.PERMISSION]: {
            message: '缺少必要的權限，請檢查瀏覽器設置。',
            actions: [
                { text: '檢查權限', action: () => checkBrowserPermissions() }
            ]
        },
        [ErrorTypes.UNKNOWN]: {
            message: '系統遇到未知錯誤，請嘗試重新加載頁面。',
            actions: [
                { text: '重新加載', action: () => location.reload() },
                { text: '聯繫支持', action: () => contactSupport() }
            ]
        }
    };
    
    return adviceMap[errorType] || {
        message: '系統遇到問題，請稍後再試。',
        actions: [
            { text: '重新加載', action: () => location.reload() }
        ]
    };
}

// 報告錯誤
function reportError(error) {
    // 這裡可以集成錯誤報告服務
    console.log('報告錯誤:', error);
    
    // 示例：發送到錯誤追蹤服務
    // fetch('/api/errors', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(error)
    // });
}

// 顯示錯誤詳情
function showErrorDetails(errorId) {
    const error = errorHistory.find(e => e.id === errorId);
    if (!error) return;
    
    const modal = document.createElement('div');
    modal.className = 'error-details-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 10px;
        padding: 20px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;
    
    content.innerHTML = `
        <h3 style="margin-top: 0; color: ${getErrorColor(error.severity)};">錯誤詳情</h3>
        <div style="margin-bottom: 15px;">
            <strong>類型:</strong> ${error.type.toUpperCase()}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>嚴重級別:</strong> ${error.severity.toUpperCase()}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>時間:</strong> ${new Date(error.timestamp).toLocaleString()}
        </div>
        <div style="margin-bottom: 15px;">
            <strong>消息:</strong> ${error.message}
        </div>
        ${error.context ? `
        <div style="margin-bottom: 15px;">
            <strong>上下文:</strong>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px;">${JSON.stringify(error.context, null, 2)}</pre>
        </div>
        ` : ''}
        ${error.error ? `
        <div style="margin-bottom: 15px;">
            <strong>錯誤堆棧:</strong>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px; font-size: 12px;">${error.error.stack || error.error.toString()}</pre>
        </div>
        ` : ''}
        <div style="text-align: right; margin-top: 20px;">
            <button onclick="this.closest('.error-details-modal').remove()" 
                    style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                關閉
            </button>
        </div>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // 點擊背景關閉
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 輔助函數
function generateErrorId() {
    return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function getErrorColor(severity) {
    const colors = {
        [ErrorSeverity.LOW]: '#28a745',
        [ErrorSeverity.MEDIUM]: '#ffc107',
        [ErrorSeverity.HIGH]: '#fd7e14',
        [ErrorSeverity.CRITICAL]: '#dc3545'
    };
    return colors[severity] || '#6c757d';
}

function getErrorIcon(type) {
    const icons = {
        [ErrorTypes.NETWORK]: '🌐',
        [ErrorTypes.STORAGE]: '💾',
        [ErrorTypes.VALIDATION]: '✅',
        [ErrorTypes.PERMISSION]: '🔒',
        [ErrorTypes.DATA_CORRUPTION]: '🗂️',
        [ErrorTypes.UI_ERROR]: '🖥️',
        [ErrorTypes.UNKNOWN]: '❓'
    };
    return icons[type] || '❌';
}

function getErrorTitle(type) {
    const titles = {
        [ErrorTypes.NETWORK]: '網絡錯誤',
        [ErrorTypes.STORAGE]: '存儲錯誤',
        [ErrorTypes.VALIDATION]: '驗證錯誤',
        [ErrorTypes.PERMISSION]: '權限錯誤',
        [ErrorTypes.DATA_CORRUPTION]: '數據損壞',
        [ErrorTypes.UI_ERROR]: '界面錯誤',
        [ErrorTypes.UNKNOWN]: '未知錯誤'
    };
    return titles[type] || '系統錯誤';
}

function formatErrorMessage(error) {
    // 格式化錯誤消息，使其更易於理解
    let message = error.message;
    
    // 移除過於技術性的細節
    message = message.replace(/undefined|null/g, '空值');
    message = message.replace(/Cannot read property/g, '無法讀取屬性');
    message = message.replace(/is not a function/g, '不是一個函數');
    
    return message;
}

// 恢復操作函數
function clearNonEssentialData() {
    if (confirm('確定要清理非必要數據嗎？這將刪除錯誤日誌和臨時數據。')) {
        // 清理錯誤歷史
        errorHistory = [];
        errorCount = 0;
        
        // 清理其他非必要數據
        localStorage.removeItem('classReadingTracker_errorLogs');
        
        showRecoveryNotification('清理完成', '非必要數據已清理完畢。');
    }
}

function exportDataForBackup() {
    if (window.dataManager) {
        window.dataManager.exportData();
    }
}

function checkNetworkConnection() {
    if (navigator.onLine) {
        showRecoveryNotification('網絡正常', '您的網絡連接正常。');
    } else {
        showRecoveryNotification('網絡離線', '請檢查您的網絡連接。');
    }
}

function checkBrowserPermissions() {
    // 這裡可以添加檢查瀏覽器權限的邏輯
    showRecoveryNotification('權限檢查', '請確保瀏覽器允許使用本地存儲。');
}

function restoreFromBackup() {
    // 這裡可以添加從備份恢復的邏輯
    showRecoveryNotification('恢復功能', '數據恢復功能正在開發中。');
}

function resetCorruptedData() {
    if (confirm('確定要重置所有數據嗎？此操作無法復原。')) {
        // 重置所有數據
        localStorage.clear();
        
        // 重新初始化
        location.reload();
    }
}

function contactSupport() {
    // 這裡可以添加聯繫支持的邏輯
    showRecoveryNotification('聯繫支持', '請通過以下方式聯繫技術支持：<EMAIL>');
}

// 日誌事件函數
function logEvent(eventData) {
    console.log('事件記錄:', eventData);
    
    // 添加到錯誤歷史記錄
    errorHistory.push({
        ...eventData,
        id: generateErrorId(),
        type: 'event'
    });
    
    // 限制錯誤歷史大小
    if (errorHistory.length > errorConfig.maxErrors) {
        errorHistory = errorHistory.slice(-errorConfig.maxErrors);
    }
    
    // 控制台日誌
    if (errorConfig.logToConsole) {
        console.group(`📝 事件記錄 [${eventData.type.toUpperCase()}]`);
        console.log('消息:', eventData.message);
        console.log('時間:', eventData.timestamp);
        
        if (eventData.context) {
            console.log('上下文:', eventData.context);
        }
        
        console.groupEnd();
    }
}

// 導出函數供其他模組使用
window.errorHandler = {
    initErrorHandler,
    handleError,
    logEvent,
    ErrorTypes,
    ErrorSeverity,
    errorHistory,
    errorCount,
    showErrorDetails,
    reportError
};

// 自動初始化 - 立即初始化以確保其他模組可以使用
initErrorHandler();

// 在 DOM 載入完成後再次確保初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果錯誤通知容器不存在，重新創建
    if (!document.getElementById('error-notification-container')) {
        createErrorNotificationContainer();
    }
});
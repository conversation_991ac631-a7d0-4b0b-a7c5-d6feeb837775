// 學生管理模組 - 負責學生相關的所有功能

// 預設學生資料（使用歷史人物）
const defaultStudents = [
    { id: '01', name: '孔子' },
    { id: '02', name: '老子' },
    { id: '03', name: '孟子' },
    { id: '04', name: '莊子' },
    { id: '05', name: '荀子' },
    { id: '06', name: '墨子' },
    { id: '07', name: '韓非子' },
    { id: '08', name: '司馬遷' },
    { id: '09', name: '諸葛亮' },
    { id: '10', name: '李白' },
    { id: '11', name: '杜甫' },
    { id: '12', name: '白居易' },
    { id: '13', name: '蘇軾' },
    { id: '14', name: '李清照' },
    { id: '15', name: '辛棄疾' },
    { id: '16', name: '朱熹' },
    { id: '17', name: '王陽明' },
    { id: '18', name: '曹雪芹' },
    { id: '19', name: '吳承恩' },
    { id: '20', name: '施耐庵' },
    { id: '21', name: '羅貫中' },
    { id: '22', name: '關漢卿' },
    { id: '23', name: '馬致遠' },
    { id: '24', name: '鄭板橋' },
    { id: '25', name: '紀曉嵐' },
    { id: '26', name: '林則徐' },
    { id: '27', name: '梁啟超' }
];

// 當前學生資料（可修改）
let students = [];

// 初始化學生管理器
function initStudentManager() {
    console.log('初始化學生管理器...');
    loadStudents();
}

// 載入學生資料
function loadStudents() {
    try {
        const stored = localStorage.getItem('classReadingTracker_students');
        if (stored) {
            students = JSON.parse(stored);
            console.log('從 localStorage 載入學生資料:', students.length, '位學生');
        } else {
            students = [...defaultStudents];
            console.log('使用預設學生資料:', students.length, '位學生');
            // 儲存到 localStorage
            saveStudents();
            console.log('已儲存預設學生資料到 localStorage');
        }

        console.log('前5位學生:', students.slice(0, 5));
        console.log('學生資料初始化完成');

    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '載入學生資料時發生錯誤: ' + error.message,
                context: { action: 'loadStudents', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('載入學生資料時發生錯誤:', error);
        }
        students = [...defaultStudents];
        console.log('使用預設學生資料作為備用');
    }
}

// 儲存學生資料
function saveStudents() {
    try {
        localStorage.setItem('classReadingTracker_students', JSON.stringify(students));
        console.log('學生資料已儲存到 localStorage');
        
        // 觸發事件通知其他模組學生資料已更新
        window.dispatchEvent(new CustomEvent('studentsUpdated', { detail: { students } }));
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.STORAGE,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '儲存學生資料時發生錯誤: ' + error.message,
                context: { action: 'saveStudents', studentsCount: students.length, error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('儲存學生資料時發生錯誤:', error);
            alert('儲存失敗，請檢查瀏覽器存儲空間');
        }
    }
}

// 獲取所有學生
function getStudents() {
    return students;
}

// 獲取單個學生
function getStudent(studentId) {
    return students.find(student => student.id === studentId);
}

// 更新學生資料
function updateStudent(studentId, updatedData) {
    const index = students.findIndex(student => student.id === studentId);
    if (index !== -1) {
        students[index] = { ...students[index], ...updatedData };
        saveStudents();
        return students[index];
    }
    return null;
}

// 重置學生名單
function resetStudents() {
    if (confirm('確定要重置為預設的歷史人物名單嗎？此操作將覆蓋目前的學生名單。')) {
        students = [...defaultStudents];
        saveStudents();
        alert('學生名單已重置為預設名單');
        return true;
    }
    return false;
}

// 批量更新學生資料
function batchUpdateStudents(updates) {
    let hasChanges = false;
    
    updates.forEach(update => {
        const student = students.find(s => s.id === update.id);
        if (student) {
            if (update.name && update.name !== student.name) {
                student.name = update.name;
                hasChanges = true;
            }
        }
    });
    
    if (hasChanges) {
        saveStudents();
    }
    
    return hasChanges;
}

// 獲取學生統計資料
function getStudentStats() {
    const books = window.dataManager ? window.dataManager.getBooks() : [];
    const stats = [];

    students.forEach(student => {
        const studentBooks = books.filter(book => book.studentId === student.id);
        const completedBooks = studentBooks.filter(book => book.status === 'completed').length;
        const readingBooks = studentBooks.filter(book => book.status === 'reading').length;
        const totalPages = studentBooks.reduce((sum, book) => sum + (book.currentPage || 0), 0);

        stats.push({
            studentId: student.id,
            studentName: student.name,
            totalBooks: studentBooks.length,
            completedBooks,
            readingBooks,
            totalPages
        });
    });

    return stats;
}

// 獲取閱讀之星
function getTopReaders(limit = 10) {
    const studentStats = getStudentStats();
    return studentStats
        .filter(stat => stat.completedBooks > 0)
        .sort((a, b) => b.completedBooks - a.completedBooks)
        .slice(0, limit);
}

// 渲染學生管理網格
function renderStudentsGrid(containerElement) {
    console.log('開始渲染學生管理網格');
    console.log('學生數量:', students.length);

    if (!containerElement) {
        console.error('找不到學生網格容器元素');
        return;
    }

    const studentsHtml = students.map(student => `
        <div class="student-item" data-student-id="${student.id}">
            <div class="student-number">${student.id}號</div>
            <div class="student-fields">
                <div class="student-field">
                    <label>姓名</label>
                    <input type="text"
                           class="student-name-input"
                           value="${student.name}"
                           data-original="${student.name}"
                           data-field="name">
                </div>
            </div>
        </div>
    `).join('');

    containerElement.innerHTML = studentsHtml;
    console.log('學生網格 HTML 已設定，長度:', studentsHtml.length);

    // 綁定輸入事件
    const inputs = containerElement.querySelectorAll('input');
    console.log('找到輸入框數量:', inputs.length);

    inputs.forEach(input => {
        input.addEventListener('input', handleStudentInputChange);
    });

    console.log('學生管理網格渲染完成');
}

// 處理學生資料輸入變更
function handleStudentInputChange(e) {
    const input = e.target;
    const studentItem = input.closest('.student-item');
    const isModified = input.value !== input.dataset.original;

    // 標記修改狀態
    if (isModified) {
        input.classList.add('modified');
        studentItem.classList.add('modified');
    } else {
        input.classList.remove('modified');

        // 檢查該學生項目是否還有其他修改
        const hasOtherModifications = studentItem.querySelectorAll('input.modified').length > 0;
        if (!hasOtherModifications) {
            studentItem.classList.remove('modified');
        }
    }
}

// 保存學生名單（從UI表單）
function saveStudentsFromGrid(containerElement) {
    const studentItems = containerElement.querySelectorAll('.student-item');
    const updates = [];

    studentItems.forEach(item => {
        const studentId = item.dataset.studentId;
        const nameInput = item.querySelector('[data-field="name"]');

        const update = { id: studentId };
        
        if (nameInput) {
            const newName = nameInput.value.trim();
            if (newName) {
                update.name = newName;
            }
        }
        
        if (Object.keys(update).length > 1) { // 有除了 id 以外的欄位
            updates.push(update);
        }
    });

    const hasChanges = batchUpdateStudents(updates);
    
    if (hasChanges) {
        // 觸發事件通知其他模組需要重新渲染書籍卡片
        window.dispatchEvent(new CustomEvent('studentsDataChanged'));
        alert('學生名單已儲存');
    } else {
        alert('沒有檢測到任何修改');
    }
    
    return hasChanges;
}

// 測試函數 - 可在控制台中調用
window.testStudentSelector = function() {
    console.log('=== 學生選擇器測試 ===');
    console.log('學生數據:', students.length, '位學生');
    console.log('前5位學生:', students.slice(0, 5));

    console.log('DOM 元素檢查:');
    const studentSelect = document.getElementById('studentSelect');
    const studentId = document.getElementById('studentId');
    console.log('  studentSelect:', studentSelect);
    console.log('  studentId:', studentId);

    if (studentSelect) {
        console.log('  studentSelect 選項數:', studentSelect.options.length);
        console.log('  前5個選項:');
        for (let i = 0; i < Math.min(5, studentSelect.options.length); i++) {
            const option = studentSelect.options[i];
            console.log(`    ${i}: ${option.value} - ${option.textContent}`);
        }
    }

    if (studentId) {
        console.log('  studentId 選項數:', studentId.options.length);
    }

    console.log('=== 測試完成 ===');
    console.log('如果選項數為1，表示只有預設選項，學生資料未正確載入');
    console.log('如果選項數大於1，表示學生資料已正確載入');
};

// 診斷學生選擇器問題
window.diagnoseStudentSelector = function() {
    console.log('=== 學生選擇器診斷 ===');

    // 檢查學生資料
    console.log('1. 學生資料檢查:');
    console.log('   students 變數:', typeof students, students ? students.length : 'undefined');
    console.log('   defaultStudents:', typeof defaultStudents, defaultStudents ? defaultStudents.length : 'undefined');

    // 檢查 DOM 元素
    console.log('2. DOM 元素檢查:');
    const studentSelect = document.getElementById('studentSelect');
    const studentId = document.getElementById('studentId');
    console.log('   studentSelect 元素:', studentSelect);
    console.log('   studentId 元素:', studentId);

    if (studentSelect) {
        console.log('   studentSelect 選項數:', studentSelect.options.length);
        console.log('   前3個選項:');
        for (let i = 0; i < Math.min(3, studentSelect.options.length); i++) {
            console.log(`     ${i}: ${studentSelect.options[i].value} - ${studentSelect.options[i].textContent}`);
        }
    }

    // 嘗試手動修復
    console.log('3. 嘗試手動修復:');
    if (!students || students.length === 0) {
        console.log('   重新初始化學生資料...');
        loadStudents();
    }

    console.log('   執行更新學生選擇器...');
    if (window.uiManager && typeof window.uiManager.updateStudentSelectors === 'function') {
        window.uiManager.updateStudentSelectors();
    }

    console.log('=== 診斷完成 ===');

    // 在頁面上顯示診斷結果
    const diagnosticDiv = document.createElement('div');
    diagnosticDiv.id = 'diagnostic-info';
    diagnosticDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: #fff;
        border: 2px solid #ff6b9d;
        border-radius: 10px;
        padding: 15px;
        max-width: 300px;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;

    const existingDiagnostic = document.getElementById('diagnostic-info');
    if (existingDiagnostic) {
        existingDiagnostic.remove();
    }

    diagnosticDiv.innerHTML = `
        <h4 style="margin: 0 0 10px 0; color: #ff6b9d;">診斷結果</h4>
        <div>學生數量: ${students ? students.length : 0}</div>
        <div>選擇器1選項: ${studentSelect ? studentSelect.options.length : 0}</div>
        <div>選擇器2選項: ${studentId ? studentId.options.length : 0}</div>
        <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">關閉</button>
    `;

    document.body.appendChild(diagnosticDiv);
};

// 導出函數供其他模組使用
window.studentManager = {
    initStudentManager,
    getStudents,
    getStudent,
    updateStudent,
    resetStudents,
    batchUpdateStudents,
    getStudentStats,
    getTopReaders,
    renderStudentsGrid,
    saveStudentsFromGrid
};
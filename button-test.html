<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按鈕功能測試</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 600px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close-btn {
            float: right;
            background: #ff6b9d !important;
            padding: 0.5rem 1rem !important;
            margin: 0 !important;
        }
        
        .students-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .student-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .student-number {
            background: #ff9a9e;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .student-field {
            margin-bottom: 0.5rem;
        }
        
        .student-field label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #495057;
        }
        
        .student-field input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 按鈕功能測試</h1>
        
        <div class="test-section">
            <h3>測試按鈕</h3>
            <button id="testManageBtn">👥 管理學生名單（測試版）</button>
            <button onclick="testButtonBinding()">測試按鈕綁定</button>
            <button onclick="testModalFunction()">測試模態框功能</button>
            <button onclick="clearDebug()">清除調試信息</button>
        </div>
        
        <div class="test-section">
            <h3>調試信息</h3>
            <div id="debugInfo" class="debug-info">點擊上方按鈕開始測試...</div>
        </div>
    </div>

    <!-- 測試模態框 -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeTestModal()">關閉</button>
            <h2>✨ 學生名單管理</h2>
            <p>這是測試模態框，用來驗證功能是否正常。</p>
            
            <div class="students-grid" id="testStudentsGrid">
                <!-- 學生項目將在這裡生成 -->
            </div>
        </div>
    </div>

    <script>
        // 測試用學生資料
        const testStudents = [
            { id: '01', name: '孔子', fullName: '孔丘' },
            { id: '02', name: '老子', fullName: '李耳' },
            { id: '03', name: '孟子', fullName: '孟軻' },
            { id: '04', name: '莊子', fullName: '莊周' },
            { id: '05', name: '荀子', fullName: '荀況' }
        ];
        
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.textContent += `[${timestamp}] ${message}\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function clearDebug() {
            document.getElementById('debugInfo').textContent = '';
        }
        
        function testButtonBinding() {
            log('=== 測試按鈕綁定 ===');
            
            const testBtn = document.getElementById('testManageBtn');
            if (testBtn) {
                log('✅ 找到測試按鈕');
                
                // 移除舊的事件監聽器
                testBtn.replaceWith(testBtn.cloneNode(true));
                const newBtn = document.getElementById('testManageBtn');
                
                // 添加新的事件監聽器
                newBtn.addEventListener('click', function() {
                    log('🎉 測試按鈕被點擊！');
                    openTestModal();
                });
                
                log('✅ 事件監聽器已綁定');
            } else {
                log('❌ 找不到測試按鈕');
            }
        }
        
        function testModalFunction() {
            log('=== 測試模態框功能 ===');
            openTestModal();
        }
        
        function openTestModal() {
            log('開啟測試模態框...');
            
            const modal = document.getElementById('testModal');
            const grid = document.getElementById('testStudentsGrid');
            
            if (!modal || !grid) {
                log('❌ 找不到模態框或網格元素');
                return;
            }
            
            // 生成學生項目
            const studentsHtml = testStudents.map(student => `
                <div class="student-item">
                    <div class="student-number">${student.id}號</div>
                    <div class="student-field">
                        <label>姓名</label>
                        <input type="text" value="${student.name}">
                    </div>
                    <div class="student-field">
                        <label>全名</label>
                        <input type="text" value="${student.fullName}">
                    </div>
                </div>
            `).join('');
            
            grid.innerHTML = studentsHtml;
            modal.classList.add('show');
            
            log('✅ 測試模態框已開啟');
            log(`✅ 生成了 ${testStudents.length} 個學生項目`);
        }
        
        function closeTestModal() {
            const modal = document.getElementById('testModal');
            modal.classList.remove('show');
            log('✅ 測試模態框已關閉');
        }
        
        // 頁面載入時自動測試
        window.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成');
            log('開始自動測試按鈕綁定...');
            testButtonBinding();
        });
        
        // 點擊模態框外部關閉
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTestModal();
            }
        });
    </script>
</body>
</html>

// 全域變數
let books = JSON.parse(localStorage.getItem('classReadingTracker_books')) || [];
let currentFilter = 'all';
let currentSort = 'student';
let currentView = 'grid';
let editingBookId = null;
let selectedStudent = '';
let userSettings = JSON.parse(localStorage.getItem('classReadingTracker_settings')) || {
    yearlyGoal: 270
};

// 預設學生資料（使用歷史人物）
const defaultStudents = [
    { id: '01', name: '孔子', fullName: '孔丘' },
    { id: '02', name: '老子', fullName: '李耳' },
    { id: '03', name: '孟子', fullName: '孟軻' },
    { id: '04', name: '莊子', fullName: '莊周' },
    { id: '05', name: '荀子', fullName: '荀況' },
    { id: '06', name: '墨子', fullName: '墨翟' },
    { id: '07', name: '韓非子', fullName: '韓非' },
    { id: '08', name: '司馬遷', fullName: '司馬遷' },
    { id: '09', name: '諸葛亮', fullName: '諸葛亮' },
    { id: '10', name: '李白', fullName: '李太白' },
    { id: '11', name: '杜甫', fullName: '杜子美' },
    { id: '12', name: '白居易', fullName: '白樂天' },
    { id: '13', name: '蘇軾', fullName: '蘇東坡' },
    { id: '14', name: '李清照', fullName: '李清照' },
    { id: '15', name: '辛棄疾', fullName: '辛幼安' },
    { id: '16', name: '朱熹', fullName: '朱元晦' },
    { id: '17', name: '王陽明', fullName: '王守仁' },
    { id: '18', name: '曹雪芹', fullName: '曹霑' },
    { id: '19', name: '吳承恩', fullName: '吳承恩' },
    { id: '20', name: '施耐庵', fullName: '施耐庵' },
    { id: '21', name: '羅貫中', fullName: '羅本' },
    { id: '22', name: '關漢卿', fullName: '關漢卿' },
    { id: '23', name: '馬致遠', fullName: '馬致遠' },
    { id: '24', name: '鄭板橋', fullName: '鄭燮' },
    { id: '25', name: '紀曉嵐', fullName: '紀昀' },
    { id: '26', name: '林則徐', fullName: '林則徐' },
    { id: '27', name: '梁啟超', fullName: '梁啟超' }
];

// 當前學生資料（可修改）
let students = [];

// 初始化學生資料
function initializeStudentData() {
    console.log('初始化學生資料...');

    try {
        const stored = localStorage.getItem('classReadingTracker_students');
        if (stored) {
            students = JSON.parse(stored);
            console.log('從 localStorage 載入學生資料:', students.length, '位學生');
        } else {
            students = [...defaultStudents];
            console.log('使用預設學生資料:', students.length, '位學生');
            // 儲存到 localStorage
            localStorage.setItem('classReadingTracker_students', JSON.stringify(students));
            console.log('已儲存預設學生資料到 localStorage');
        }

        console.log('前5位學生:', students.slice(0, 5));
        console.log('學生資料初始化完成');

    } catch (error) {
        console.error('載入學生資料時發生錯誤:', error);
        students = [...defaultStudents];
        console.log('使用預設學生資料作為備用');
    }
}

// DOM 元素 - 將在 DOM 載入後初始化
let elements = {};

// 初始化 DOM 元素
function initializeElements() {
    console.log('初始化 DOM 元素...');

    elements = {
        // 按鈕
        addBookBtn: document.getElementById('addBookBtn'),
        setGoalBtn: document.getElementById('setGoalBtn'),
        manageStudentsBtn: document.getElementById('manageStudentsBtn'),
        closeModal: document.getElementById('closeModal'),
        closeGoalModal: document.getElementById('closeGoalModal'),
        closeDetailModal: document.getElementById('closeDetailModal'),
        closeStudentsModal: document.getElementById('closeStudentsModal'),
        cancelBtn: document.getElementById('cancelBtn'),
        cancelGoalBtn: document.getElementById('cancelGoalBtn'),
        showAllStudents: document.getElementById('showAllStudents'),
        showTopReaders: document.getElementById('showTopReaders'),
        resetStudentsBtn: document.getElementById('resetStudentsBtn'),
        saveStudentsBtn: document.getElementById('saveStudentsBtn'),

        // 模態框
        bookModal: document.getElementById('bookModal'),
        goalModal: document.getElementById('goalModal'),
        bookDetailModal: document.getElementById('bookDetailModal'),
        studentsModal: document.getElementById('studentsModal'),

        // 表單
        bookForm: document.getElementById('bookForm'),
        goalForm: document.getElementById('goalForm'),

        // 輸入欄位
        searchInput: document.getElementById('searchInput'),
        sortSelect: document.getElementById('sortSelect'),
        studentSelect: document.getElementById('studentSelect'),
        studentId: document.getElementById('studentId'),

        // 容器
        booksContainer: document.getElementById('booksContainer'),
        emptyState: document.getElementById('emptyState'),

        // 統計
        totalBooks: document.getElementById('totalBooks'),
        completedBooks: document.getElementById('completedBooks'),
        readingBooks: document.getElementById('readingBooks'),
        totalPages: document.getElementById('totalPages'),
        activeStudents: document.getElementById('activeStudents'),
        goalProgress: document.getElementById('goalProgress'),
        goalTarget: document.getElementById('goalTarget'),
        goalProgressBar: document.getElementById('goalProgressBar'),
        avgPerStudent: document.getElementById('avgPerStudent'),

        // 其他
        modalTitle: document.getElementById('modalTitle'),
        ratingInput: document.getElementById('ratingInput'),
        studentsGrid: document.getElementById('studentsGrid')
    };

    // 檢查關鍵元素
    console.log('studentSelect 元素:', elements.studentSelect);
    console.log('studentId 元素:', elements.studentId);
    console.log('manageStudentsBtn 元素:', elements.manageStudentsBtn);

    console.log('DOM 元素初始化完成');
}

// 測試函數 - 可在控制台中調用
window.testStudentSelector = function() {
    console.log('=== 學生選擇器測試 ===');
    console.log('學生數據:', students.length, '位學生');
    console.log('前5位學生:', students.slice(0, 5));

    console.log('DOM 元素檢查:');
    console.log('  studentSelect:', elements.studentSelect);
    console.log('  studentId:', elements.studentId);

    if (elements.studentSelect) {
        console.log('  studentSelect 選項數:', elements.studentSelect.options.length);
        console.log('  前5個選項:');
        for (let i = 0; i < Math.min(5, elements.studentSelect.options.length); i++) {
            const option = elements.studentSelect.options[i];
            console.log(`    ${i}: ${option.value} - ${option.textContent}`);
        }
    }

    if (elements.studentId) {
        console.log('  studentId 選項數:', elements.studentId.options.length);
    }

    console.log('=== 測試完成 ===');
    console.log('如果選項數為1，表示只有預設選項，學生資料未正確載入');
    console.log('如果選項數大於1，表示學生資料已正確載入');
};

// 強制更新學生選擇器的測試函數
window.forceUpdateSelectors = function() {
    console.log('強制更新學生選擇器...');
    if (typeof updateStudentSelectors === 'function') {
        updateStudentSelectors();
        console.log('更新完成，請檢查選擇器');
    } else {
        console.error('找不到 updateStudentSelectors 函數');
    }
};

// 診斷學生選擇器問題
window.diagnoseStudentSelector = function() {
    console.log('=== 學生選擇器診斷 ===');

    // 檢查學生資料
    console.log('1. 學生資料檢查:');
    console.log('   students 變數:', typeof students, students ? students.length : 'undefined');
    console.log('   defaultStudents:', typeof defaultStudents, defaultStudents ? defaultStudents.length : 'undefined');

    // 檢查 DOM 元素
    console.log('2. DOM 元素檢查:');
    const studentSelect = document.getElementById('studentSelect');
    const studentId = document.getElementById('studentId');
    console.log('   studentSelect 元素:', studentSelect);
    console.log('   studentId 元素:', studentId);

    if (studentSelect) {
        console.log('   studentSelect 選項數:', studentSelect.options.length);
        console.log('   前3個選項:');
        for (let i = 0; i < Math.min(3, studentSelect.options.length); i++) {
            console.log(`     ${i}: ${studentSelect.options[i].value} - ${studentSelect.options[i].textContent}`);
        }
    }

    // 檢查 elements 物件
    console.log('3. elements 物件檢查:');
    console.log('   elements:', typeof elements);
    console.log('   elements.studentSelect:', elements.studentSelect);
    console.log('   elements.studentId:', elements.studentId);

    // 嘗試手動修復
    console.log('4. 嘗試手動修復:');
    if (!students || students.length === 0) {
        console.log('   重新初始化學生資料...');
        initializeStudentData();
    }

    if (!elements.studentSelect || !elements.studentId) {
        console.log('   重新初始化 DOM 元素...');
        initializeElements();
    }

    console.log('   執行更新學生選擇器...');
    updateStudentSelectors();

    console.log('=== 診斷完成 ===');

    // 在頁面上顯示診斷結果
    const diagnosticDiv = document.createElement('div');
    diagnosticDiv.id = 'diagnostic-info';
    diagnosticDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: #fff;
        border: 2px solid #ff6b9d;
        border-radius: 10px;
        padding: 15px;
        max-width: 300px;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;

    const existingDiagnostic = document.getElementById('diagnostic-info');
    if (existingDiagnostic) {
        existingDiagnostic.remove();
    }

    diagnosticDiv.innerHTML = `
        <h4 style="margin: 0 0 10px 0; color: #ff6b9d;">診斷結果</h4>
        <div>學生數量: ${students ? students.length : 0}</div>
        <div>選擇器1選項: ${studentSelect ? studentSelect.options.length : 0}</div>
        <div>選擇器2選項: ${studentId ? studentId.options.length : 0}</div>
        <div>elements.studentSelect: ${elements.studentSelect ? '✅' : '❌'}</div>
        <div>elements.studentId: ${elements.studentId ? '✅' : '❌'}</div>
        <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">關閉</button>
    `;

    document.body.appendChild(diagnosticDiv);
};

// 初始化應用
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM 載入完成，開始初始化...');

    // 延遲一點確保所有元素都已載入
    setTimeout(() => {
        console.log('開始完整初始化流程...');

        // 1. 初始化學生資料
        initializeStudentData();

        // 2. 初始化 DOM 元素
        initializeElements();

        // 3. 初始化應用
        initializeApp();

        // 4. 綁定事件
        bindEvents();

        // 5. 渲染內容
        renderBooks();
        updateStats();

        // 6. 強制更新學生選擇器（確保顯示）
        console.log('強制更新學生選擇器...');
        updateStudentSelectors();

        // 7. 自動執行測試
        console.log('自動執行學生選擇器測試...');
        if (typeof window.testStudentSelector === 'function') {
            window.testStudentSelector();
        }

        console.log('完整初始化流程完成');
    }, 200);
});

// 初始化應用
function initializeApp() {
    console.log('開始初始化應用...');
    console.log('學生數據:', students.length, '位學生');
    console.log('前3位學生:', students.slice(0, 3));
    console.log('關鍵 DOM 元素檢查:');
    console.log('  - studentSelect:', elements.studentSelect);
    console.log('  - studentId:', elements.studentId);
    console.log('  - goalTarget:', elements.goalTarget);

    // 初始化學生選單
    console.log('開始初始化學生選單...');
    initializeStudentSelectors();

    // 設定目標顯示
    if (elements.goalTarget) {
        elements.goalTarget.textContent = userSettings.yearlyGoal;
        console.log('設定年度目標:', userSettings.yearlyGoal);
    } else {
        console.error('找不到 goalTarget 元素');
    }

    if (elements.avgPerStudent) {
        elements.avgPerStudent.textContent = Math.round(userSettings.yearlyGoal / 27);
        console.log('設定平均每人目標:', Math.round(userSettings.yearlyGoal / 27));
    } else {
        console.error('找不到 avgPerStudent 元素');
    }

    // 如果沒有書籍，顯示空狀態
    if (books.length === 0) {
        if (elements.emptyState) {
            elements.emptyState.style.display = 'block';
            console.log('顯示空狀態');
        } else {
            console.error('找不到 emptyState 元素');
        }
    }

    console.log('應用初始化完成');
}

// 初始化學生選單
function initializeStudentSelectors() {
    updateStudentSelectors();
}

// 綁定事件
function bindEvents() {
    console.log('開始綁定事件...');

    // 檢查關鍵元素是否存在
    if (!elements.manageStudentsBtn) {
        console.error('找不到 manageStudentsBtn 元素');
    } else {
        console.log('找到 manageStudentsBtn 元素');
    }

    // 模態框事件
    elements.addBookBtn?.addEventListener('click', () => openBookModal());
    elements.setGoalBtn?.addEventListener('click', () => openGoalModal());

    if (elements.manageStudentsBtn) {
        elements.manageStudentsBtn.addEventListener('click', () => {
            console.log('管理學生名單按鈕被點擊');
            
            // 檢查是否使用模組化系統
            if (window.modalManager && typeof window.dispatchEvent === 'function') {
                // 觸發模組化系統的事件
                window.dispatchEvent(new CustomEvent('openStudentsModal'));
            } else {
                // 使用原始函數
                openStudentsModal();
            }
        });
    }

    elements.closeModal?.addEventListener('click', () => closeModal('bookModal'));
    elements.closeGoalModal?.addEventListener('click', () => closeModal('goalModal'));
    elements.closeDetailModal?.addEventListener('click', () => closeModal('bookDetailModal'));
    elements.closeStudentsModal?.addEventListener('click', () => closeModal('studentsModal'));
    elements.cancelBtn?.addEventListener('click', () => closeModal('bookModal'));
    elements.cancelGoalBtn?.addEventListener('click', () => closeModal('goalModal'));
    
    // 表單提交
    elements.bookForm.addEventListener('submit', handleBookSubmit);
    elements.goalForm.addEventListener('submit', handleGoalSubmit);
    
    // 搜尋和篩選
    elements.searchInput.addEventListener('input', handleSearch);
    elements.sortSelect.addEventListener('change', handleSort);
    elements.studentSelect.addEventListener('change', handleStudentFilter);

    // 篩選按鈕
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            // 只對狀態篩選按鈕處理 active 狀態
            if (e.target.dataset.status) {
                document.querySelectorAll('.filter-btn[data-status]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                currentFilter = e.target.dataset.status;
                renderBooks();
            }
        });
    });

    // 學生篩選按鈕
    elements.showAllStudents.addEventListener('click', () => {
        selectedStudent = '';
        elements.studentSelect.value = '';
        renderBooks();
    });

    elements.showTopReaders.addEventListener('click', () => {
        showTopReaders();
    });

    // 學生管理事件
    elements.resetStudentsBtn.addEventListener('click', resetStudents);
    elements.saveStudentsBtn.addEventListener('click', saveStudents);
    
    // 視圖切換
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            currentView = e.target.dataset.view;
            toggleView();
        });
    });
    
    // 評分輸入
    elements.ratingInput.addEventListener('click', handleRatingClick);
    
    // 點擊模態框外部關閉
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });
}

// 開啟書籍模態框
function openBookModal(bookId = null) {
    editingBookId = bookId;
    const book = bookId ? books.find(b => b.id === bookId) : null;

    elements.modalTitle.textContent = book ? '編輯閱讀記錄' : '新增閱讀記錄';

    if (book) {
        // 填入現有資料
        elements.studentId.value = book.studentId || '';
        document.getElementById('bookTitle').value = book.title || '';
        document.getElementById('bookAuthor').value = book.author || '';
        document.getElementById('totalPages').value = book.totalPages || '';
        document.getElementById('currentPage').value = book.currentPage || 0;
        document.getElementById('bookStatus').value = book.status || 'to-read';
        document.getElementById('bookCover').value = book.coverImage || '';
        document.getElementById('bookTags').value = book.tags ? book.tags.join(', ') : '';
        document.getElementById('bookNotes').value = book.notes || '';

        // 設定評分
        setRating(book.rating || 0);
    } else {
        // 清空表單
        elements.bookForm.reset();
        setRating(0);

        // 如果有選中的學生，預設選擇
        if (selectedStudent) {
            elements.studentId.value = selectedStudent;
        }
    }

    showModal('bookModal');
}

// 開啟目標設定模態框
function openGoalModal() {
    document.getElementById('yearlyGoal').value = userSettings.yearlyGoal;
    showModal('goalModal');
}

// 開啟學生管理模態框
function openStudentsModal() {
    console.log('開啟學生管理模態框');
    console.log('學生數據:', students);
    console.log('studentsGrid 元素:', elements.studentsGrid);

    try {
        // 檢查學生數據
        if (!students || students.length === 0) {
            console.error('學生數據為空，重新初始化...');
            initializeStudentData();
        }
        
        // 檢查 DOM 元素
        if (!elements.studentsGrid) {
            console.error('找不到 studentsGrid 元素，重新初始化...');
            initializeElements();
        }
        
        // 再次檢查
        if (!students || students.length === 0) {
            throw new Error('學生數據初始化失敗');
        }
        
        if (!elements.studentsGrid) {
            throw new Error('無法找到 studentsGrid 元素');
        }
        
        renderStudentsGrid();
        showModal('studentsModal');
        console.log('學生管理模態框已開啟');
    } catch (error) {
        console.error('開啟學生管理模態框時發生錯誤:', error);
        alert('開啟學生管理模態框時發生錯誤: ' + error.message);
    }
}

// 顯示模態框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// 關閉模態框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    
    if (modalId === 'bookModal') {
        editingBookId = null;
        elements.bookForm.reset();
        setRating(0);
    }
}

// 處理書籍表單提交
function handleBookSubmit(e) {
    e.preventDefault();

    const studentId = elements.studentId.value;
    const bookData = {
        id: editingBookId || generateId(),
        studentId: studentId,
        title: document.getElementById('bookTitle').value.trim(),
        author: document.getElementById('bookAuthor').value.trim(),
        totalPages: parseInt(document.getElementById('totalPages').value) || 0,
        currentPage: parseInt(document.getElementById('currentPage').value) || 0,
        status: document.getElementById('bookStatus').value,
        coverImage: document.getElementById('bookCover').value.trim(),
        tags: document.getElementById('bookTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
        notes: document.getElementById('bookNotes').value.trim(),
        rating: getCurrentRating(),
        dateAdded: editingBookId ? books.find(b => b.id === editingBookId).dateAdded : new Date().toISOString(),
        dateModified: new Date().toISOString()
    };

    // 驗證必填欄位
    if (!studentId) {
        alert('請選擇學生');
        return;
    }

    if (!bookData.title) {
        alert('請輸入書名');
        return;
    }

    // 驗證頁數
    if (bookData.currentPage > bookData.totalPages && bookData.totalPages > 0) {
        alert('目前頁數不能超過總頁數');
        return;
    }

    if (editingBookId) {
        // 更新現有書籍
        const index = books.findIndex(b => b.id === editingBookId);
        books[index] = bookData;
    } else {
        // 新增書籍
        books.push(bookData);
    }

    saveBooks();
    renderBooks();
    updateStats();
    closeModal('bookModal');
}

// 處理目標設定提交
function handleGoalSubmit(e) {
    e.preventDefault();

    const yearlyGoal = parseInt(document.getElementById('yearlyGoal').value);
    if (yearlyGoal > 0) {
        userSettings.yearlyGoal = yearlyGoal;
        localStorage.setItem('classReadingTracker_settings', JSON.stringify(userSettings));
        elements.goalTarget.textContent = yearlyGoal;
        elements.avgPerStudent.textContent = Math.round(yearlyGoal / 27);
        updateStats();
        closeModal('goalModal');
    }
}

// 處理學生篩選
function handleStudentFilter(e) {
    selectedStudent = e.target.value;
    renderBooks();
}

// 處理搜尋
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    renderBooks(searchTerm);
}

// 顯示閱讀之星
function showTopReaders() {
    const studentStats = getStudentStats();
    const topReaders = studentStats
        .filter(stat => stat.completedBooks > 0)
        .sort((a, b) => b.completedBooks - a.completedBooks)
        .slice(0, 10);

    if (topReaders.length === 0) {
        alert('還沒有學生完成閱讀！');
        return;
    }

    let message = '🌟 班級閱讀之星 🌟\n\n';
    topReaders.forEach((student, index) => {
        const studentInfo = students.find(s => s.id === student.studentId);
        message += `${index + 1}. ${studentInfo.name}：${student.completedBooks}本\n`;
    });

    alert(message);
}

// 處理排序
function handleSort(e) {
    currentSort = e.target.value;
    renderBooks();
}

// 處理評分點擊
function handleRatingClick(e) {
    if (e.target.tagName === 'I') {
        const rating = parseInt(e.target.dataset.rating);
        setRating(rating);
    }
}

// 設定評分
function setRating(rating) {
    const stars = elements.ratingInput.querySelectorAll('i');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
}

// 獲取當前評分
function getCurrentRating() {
    return elements.ratingInput.querySelectorAll('i.active').length;
}

// 切換視圖
function toggleView() {
    if (currentView === 'list') {
        elements.booksContainer.classList.add('list-view');
    } else {
        elements.booksContainer.classList.remove('list-view');
    }
}

// 生成唯一 ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 儲存書籍資料
function saveBooks() {
    localStorage.setItem('classReadingTracker_books', JSON.stringify(books));
}

// 獲取學生統計資料
function getStudentStats() {
    const stats = [];

    students.forEach(student => {
        const studentBooks = books.filter(book => book.studentId === student.id);
        const completedBooks = studentBooks.filter(book => book.status === 'completed').length;
        const readingBooks = studentBooks.filter(book => book.status === 'reading').length;
        const totalPages = studentBooks.reduce((sum, book) => sum + (book.currentPage || 0), 0);

        stats.push({
            studentId: student.id,
            studentName: student.name,
            totalBooks: studentBooks.length,
            completedBooks,
            readingBooks,
            totalPages
        });
    });

    return stats;
}

// 獲取學生資訊
function getStudentInfo(studentId) {
    return students.find(s => s.id === studentId);
}

// 渲染學生管理網格
function renderStudentsGrid() {
    console.log('開始渲染學生管理網格');
    
    // 檢查學生數據
    if (!students || students.length === 0) {
        console.error('學生數據為空，無法渲染學生網格');
        throw new Error('學生數據為空');
    }
    
    console.log('學生數量:', students.length);

    // 檢查 DOM 元素
    if (!elements.studentsGrid) {
        console.error('找不到 studentsGrid 元素');
        throw new Error('找不到 studentsGrid 元素');
    }

    try {
        // 檢查每個學生對象的結構
        for (let i = 0; i < Math.min(3, students.length); i++) {
            const student = students[i];
            console.log(`學生 ${i+1}:`, student);
            if (!student.id || !student.name || !student.fullName) {
                console.error(`學生 ${i+1} 數據結構不完整:`, student);
                throw new Error(`學生數據結構不完整`);
            }
        }

        const studentsHtml = students.map(student => {
            // 確保學生對象有必要的屬性
            if (!student.id || !student.name || !student.fullName) {
                console.error('學生數據結構不完整:', student);
                return '';
            }
            
            return `
                <div class="student-item" data-student-id="${student.id}">
                    <div class="student-number">${student.id}號</div>
                    <div class="student-fields">
                        <div class="student-field">
                            <label>姓名</label>
                            <input type="text"
                                   class="student-name-input"
                                   value="${student.name}"
                                   data-original="${student.name}"
                                   data-field="name">
                        </div>
                        <div class="student-field">
                            <label>全名</label>
                            <input type="text"
                                   class="student-fullname-input"
                                   value="${student.fullName}"
                                   data-original="${student.fullName}"
                                   data-field="fullName">
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        if (!studentsHtml.trim()) {
            throw new Error('生成的學生網格 HTML 為空');
        }

        elements.studentsGrid.innerHTML = studentsHtml;
        console.log('學生網格 HTML 已設定，長度:', studentsHtml.length);

        // 綁定輸入事件
        const inputs = elements.studentsGrid.querySelectorAll('input');
        console.log('找到輸入框數量:', inputs.length);

        if (inputs.length === 0) {
            console.warn('沒有找到任何輸入框，可能影響功能');
        }

        inputs.forEach(input => {
            input.addEventListener('input', handleStudentInputChange);
        });

        console.log('學生管理網格渲染完成');
    } catch (error) {
        console.error('渲染學生網格時發生錯誤:', error);
        throw error; // 重新拋出錯誤，讓上層處理
    }
}

// 處理學生資料輸入變更
function handleStudentInputChange(e) {
    try {
        const input = e.target;
        const studentItem = input.closest('.student-item');
        
        if (!studentItem) {
            console.error('找不到學生項目元素');
            return;
        }
        
        const isModified = input.value !== input.dataset.original;

        // 標記修改狀態
        if (isModified) {
            input.classList.add('modified');
            studentItem.classList.add('modified');
        } else {
            input.classList.remove('modified');

            // 檢查該學生項目是否還有其他修改
            const hasOtherModifications = studentItem.querySelectorAll('input.modified').length > 0;
            if (!hasOtherModifications) {
                studentItem.classList.remove('modified');
            }
        }
    } catch (error) {
        console.error('處理學生資料輸入變更時發生錯誤:', error);
    }
}

// 重置學生名單
function resetStudents() {
    try {
        if (confirm('確定要重置為預設的歷史人物名單嗎？此操作將覆蓋目前的學生名單。')) {
            // 檢查預設學生資料
            if (!defaultStudents || defaultStudents.length === 0) {
                throw new Error('預設學生資料為空');
            }
            
            // 重置學生資料
            students = [...defaultStudents];
            
            // 儲存到 localStorage
            localStorage.setItem('classReadingTracker_students', JSON.stringify(students));
            console.log('學生資料已重置並儲存到 localStorage');
            
            // 更新UI
            renderStudentsGrid();
            updateStudentSelectors();
            
            alert('學生名單已重置為預設名單');
            console.log('學生名單重置完成');
        }
    } catch (error) {
        console.error('重置學生名單時發生錯誤:', error);
        alert('重置學生名單時發生錯誤: ' + error.message);
    }
}

// 儲存學生名單
function saveStudents() {
    try {
        if (!elements.studentsGrid) {
            throw new Error('找不到 studentsGrid 元素');
        }

        const studentItems = elements.studentsGrid.querySelectorAll('.student-item');
        if (!studentItems || studentItems.length === 0) {
            throw new Error('找不到任何學生項目');
        }

        let hasChanges = false;
        const updatedStudents = [...students]; // 創建副本以避免直接修改

        studentItems.forEach(item => {
            const studentId = item.dataset.studentId;
            const nameInput = item.querySelector('[data-field="name"]');
            const fullNameInput = item.querySelector('[data-field="fullName"]');

            if (!studentId || !nameInput || !fullNameInput) {
                console.error('學生項目缺少必要的元素:', item);
                return;
            }

            const student = updatedStudents.find(s => s.id === studentId);
            if (student) {
                const newName = nameInput.value.trim();
                const newFullName = fullNameInput.value.trim();

                if (newName && newName !== student.name) {
                    student.name = newName;
                    hasChanges = true;
                    console.log(`更新學生 ${studentId} 姓名: ${newName}`);
                }

                if (newFullName && newFullName !== student.fullName) {
                    student.fullName = newFullName;
                    hasChanges = true;
                    console.log(`更新學生 ${studentId} 全名: ${newFullName}`);
                }
            } else {
                console.error('找不到學生資料:', studentId);
            }
        });

        if (hasChanges) {
            // 更新全局學生資料
            students = updatedStudents;
            
            // 儲存到 localStorage
            localStorage.setItem('classReadingTracker_students', JSON.stringify(students));
            console.log('學生資料已儲存到 localStorage');
            
            // 更新UI
            updateStudentSelectors();
            renderBooks(); // 重新渲染書籍卡片以顯示新的學生姓名
            closeModal('studentsModal');
            
            alert('學生名單已儲存');
            console.log('學生名單儲存完成');
        } else {
            alert('沒有檢測到任何修改');
            console.log('沒有檢測到任何修改');
        }
    } catch (error) {
        console.error('儲存學生名單時發生錯誤:', error);
        alert('儲存學生名單時發生錯誤: ' + error.message);
    }
}

// 更新學生選擇器
function updateStudentSelectors() {
    console.log('=== 開始更新學生選擇器 ===');
    console.log('學生數據:', students.length, '位學生');
    console.log('前3位學生:', students.slice(0, 3));

    // 檢查元素是否存在
    console.log('檢查 DOM 元素:');
    console.log('  studentSelect:', elements.studentSelect);
    console.log('  studentId:', elements.studentId);

    if (!elements.studentSelect) {
        console.error('❌ 找不到 studentSelect 元素');
        // 嘗試重新獲取元素
        elements.studentSelect = document.getElementById('studentSelect');
        console.log('重新獲取 studentSelect:', elements.studentSelect);
    }
    if (!elements.studentId) {
        console.error('❌ 找不到 studentId 元素');
        // 嘗試重新獲取元素
        elements.studentId = document.getElementById('studentId');
        console.log('重新獲取 studentId:', elements.studentId);
    }

    // 如果還是找不到元素，直接返回
    if (!elements.studentSelect || !elements.studentId) {
        console.error('❌ 無法找到必要的選擇器元素，停止更新');
        return;
    }

    console.log('✅ 元素檢查通過，開始更新選項');

    // 清空現有選項
    elements.studentSelect.innerHTML = '<option value="">🎓 選擇學生</option>';
    elements.studentId.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
    console.log('已清空現有選項');

    // 檢查學生資料
    if (students.length === 0) {
        console.error('❌ 學生資料為空，無法添加選項');
        return;
    }

    console.log('開始添加學生選項...');
    // 重新添加學生選項
    students.forEach((student, index) => {
        // 頂部學生選擇器
        const option1 = document.createElement('option');
        option1.value = student.id;
        option1.textContent = `${student.id}號 ${student.name}`;
        elements.studentSelect.appendChild(option1);

        // 表單中的學生選擇器
        const option2 = document.createElement('option');
        option2.value = student.id;
        option2.textContent = `${student.id}號 ${student.name}（${student.fullName}）`;
        elements.studentId.appendChild(option2);

        // 只記錄前3個學生的添加過程
        if (index < 3) {
            console.log(`添加學生 ${index + 1}: ${student.id}號 ${student.name}`);
        }
    });

    // 驗證結果
    console.log('驗證更新結果:');
    console.log('  studentSelect 選項數:', elements.studentSelect.options.length);
    console.log('  studentId 選項數:', elements.studentId.options.length);

    if (elements.studentSelect.options.length > 1) {
        console.log('✅ studentSelect 更新成功');
    } else {
        console.error('❌ studentSelect 更新失敗');
    }

    if (elements.studentId.options.length > 1) {
        console.log('✅ studentId 更新成功');
    } else {
        console.error('❌ studentId 更新失敗');
    }

    console.log('=== 學生選擇器更新完成 ===');
}

// 渲染書籍列表
function renderBooks(searchTerm = '') {
    let filteredBooks = books;

    // 篩選學生
    if (selectedStudent) {
        filteredBooks = filteredBooks.filter(book => book.studentId === selectedStudent);
    }

    // 篩選狀態
    if (currentFilter !== 'all') {
        filteredBooks = filteredBooks.filter(book => book.status === currentFilter);
    }

    // 搜尋
    if (searchTerm) {
        filteredBooks = filteredBooks.filter(book => {
            const student = getStudentInfo(book.studentId);
            return book.title.toLowerCase().includes(searchTerm) ||
                   (book.author && book.author.toLowerCase().includes(searchTerm)) ||
                   (student && student.name.toLowerCase().includes(searchTerm)) ||
                   (book.tags && book.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
        });
    }

    // 排序
    filteredBooks.sort((a, b) => {
        switch (currentSort) {
            case 'student':
                return a.studentId.localeCompare(b.studentId);
            case 'title':
                return a.title.localeCompare(b.title);
            case 'author':
                return (a.author || '').localeCompare(b.author || '');
            case 'progress':
                const progressA = a.totalPages > 0 ? (a.currentPage / a.totalPages) : 0;
                const progressB = b.totalPages > 0 ? (b.currentPage / b.totalPages) : 0;
                return progressB - progressA;
            case 'rating':
                return (b.rating || 0) - (a.rating || 0);
            case 'dateAdded':
                return new Date(b.dateAdded) - new Date(a.dateAdded);
            default:
                return 0;
        }
    });

    // 渲染
    if (filteredBooks.length === 0) {
        elements.booksContainer.innerHTML = '';
        elements.emptyState.style.display = 'block';
    } else {
        elements.emptyState.style.display = 'none';
        elements.booksContainer.innerHTML = filteredBooks.map(book => createBookCard(book)).join('');

        // 綁定卡片事件
        bindBookCardEvents();
    }
}

// 創建書籍卡片
function createBookCard(book) {
    const progress = book.totalPages > 0 ? Math.round((book.currentPage / book.totalPages) * 100) : 0;
    const statusClass = `status-${book.status}`;
    const statusText = getStatusText(book.status);
    const student = getStudentInfo(book.studentId);

    return `
        <div class="book-card" data-book-id="${book.id}">
            <div class="book-header">
                <div class="book-info">
                    <div class="student-name">
                        <span class="student-number">${student.id}</span>
                        ${student.name}
                    </div>
                    <h3>${escapeHtml(book.title)}</h3>
                    ${book.author ? `<div class="author">作者：${escapeHtml(book.author)}</div>` : ''}
                </div>
                <span class="book-status ${statusClass}">${statusText}</span>
            </div>

            ${book.tags && book.tags.length > 0 ? `
                <div class="book-tags">
                    ${book.tags.map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('')}
                </div>
            ` : ''}

            ${book.totalPages > 0 ? `
                <div class="book-progress">
                    <div class="progress-text">
                        <span>進度</span>
                        <span>${book.currentPage} / ${book.totalPages} 頁 (${progress}%)</span>
                    </div>
                    <div class="progress-bar-book">
                        <div class="progress-fill-book" style="width: ${progress}%"></div>
                    </div>
                </div>
            ` : ''}

            <div class="book-meta">
                <div class="book-rating">
                    ${generateStarRating(book.rating || 0)}
                </div>
                <div class="book-actions">
                    <button class="action-btn edit" onclick="openBookModal('${book.id}')" title="編輯">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteBook('${book.id}')" title="刪除">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="action-btn" onclick="showBookDetail('${book.id}')" title="詳情">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 綁定書籍卡片事件
function bindBookCardEvents() {
    document.querySelectorAll('.book-card').forEach(card => {
        card.addEventListener('click', (e) => {
            // 如果點擊的是按鈕，不觸發卡片點擊
            if (e.target.closest('.book-actions')) {
                return;
            }

            const bookId = card.dataset.bookId;
            showBookDetail(bookId);
        });
    });
}

// 生成星級評分
function generateStarRating(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="fas fa-star empty"></i>';
        }
    }
    return stars;
}

// 獲取狀態文字
function getStatusText(status) {
    const statusMap = {
        'to-read': '待讀',
        'reading': '閱讀中',
        'completed': '已完成',
        'abandoned': '暫停'
    };
    return statusMap[status] || '未知';
}

// HTML 轉義
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 刪除書籍
function deleteBook(bookId) {
    if (confirm('確定要刪除這本書嗎？此操作無法復原。')) {
        books = books.filter(book => book.id !== bookId);
        saveBooks();
        renderBooks();
        updateStats();
    }
}

// 顯示書籍詳情
function showBookDetail(bookId) {
    const book = books.find(b => b.id === bookId);
    if (!book) return;

    const progress = book.totalPages > 0 ? Math.round((book.currentPage / book.totalPages) * 100) : 0;
    const statusText = getStatusText(book.status);
    const student = getStudentInfo(book.studentId);

    const detailContent = `
        <div class="detail-header">
            ${book.coverImage ? `<img src="${book.coverImage}" alt="${book.title}" class="detail-cover">` : ''}
            <div class="detail-info">
                <div class="student-info">
                    <span class="student-number">${student.id}</span>
                    <strong>${student.name}</strong>（${student.fullName}）
                </div>
                <h2>${escapeHtml(book.title)}</h2>
                ${book.author ? `<div class="author">作者：${escapeHtml(book.author)}</div>` : ''}
                <div class="book-rating">
                    ${generateStarRating(book.rating || 0)}
                </div>
                ${book.tags && book.tags.length > 0 ? `
                    <div class="book-tags">
                        ${book.tags.map(tag => `<span class="tag">${escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        </div>

        <div class="detail-meta">
            <div class="meta-item">
                <span class="meta-value">${statusText}</span>
                <span class="meta-label">狀態</span>
            </div>
            ${book.totalPages > 0 ? `
                <div class="meta-item">
                    <span class="meta-value">${progress}%</span>
                    <span class="meta-label">進度</span>
                </div>
                <div class="meta-item">
                    <span class="meta-value">${book.currentPage}</span>
                    <span class="meta-label">目前頁數</span>
                </div>
                <div class="meta-item">
                    <span class="meta-value">${book.totalPages}</span>
                    <span class="meta-label">總頁數</span>
                </div>
            ` : ''}
            <div class="meta-item">
                <span class="meta-value">${book.rating || 0}/5</span>
                <span class="meta-label">評分</span>
            </div>
        </div>

        ${book.notes ? `
            <div class="detail-notes">
                <h3>閱讀筆記</h3>
                <p>${escapeHtml(book.notes).replace(/\n/g, '<br>')}</p>
            </div>
        ` : ''}
    `;

    document.getElementById('bookDetailContent').innerHTML = detailContent;
    showModal('bookDetailModal');
}

// 更新統計資料
function updateStats() {
    const totalBooks = books.length;
    const completedBooks = books.filter(book => book.status === 'completed').length;
    const readingBooks = books.filter(book => book.status === 'reading').length;
    const totalPages = books.reduce((sum, book) => sum + (book.currentPage || 0), 0);

    // 計算活躍學生數（有閱讀記錄的學生）
    const activeStudentIds = [...new Set(books.map(book => book.studentId))];
    const activeStudents = activeStudentIds.length;

    // 更新顯示
    elements.totalBooks.textContent = totalBooks;
    elements.completedBooks.textContent = completedBooks;
    elements.readingBooks.textContent = readingBooks;
    elements.totalPages.textContent = totalPages.toLocaleString();
    elements.activeStudents.textContent = activeStudents;

    // 更新目標進度
    elements.goalProgress.textContent = completedBooks;
    const goalPercentage = Math.min((completedBooks / userSettings.yearlyGoal) * 100, 100);
    elements.goalProgressBar.style.width = `${goalPercentage}%`;
}

// 匯出資料
function exportData() {
    try {
        // 使用新的模組化結構獲取數據
        const booksData = window.dataManager ? window.dataManager.getBooks() : books;
        const studentsData = window.studentManager ? window.studentManager.getStudents() : students;
        const settingsData = window.dataManager ? window.dataManager.getUserSettings() : userSettings;
        
        const data = {
            books: booksData,
            students: studentsData,
            settings: settingsData,
            exportDate: new Date().toISOString(),
            version: '1.1'
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `reading-tracker-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        // 使用新的錯誤處理系統記錄成功事件
        if (window.errorHandler) {
            window.errorHandler.logEvent({
                type: 'export',
                message: '數據匯出成功',
                context: {
                    booksCount: booksData.length,
                    studentsCount: studentsData.length,
                    exportDate: data.exportDate
                },
                timestamp: new Date().toISOString()
            });
        }
        
        console.log('數據匯出成功');
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UNKNOWN,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '數據匯出失敗: ' + error.message,
                context: { action: 'exportData', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('數據匯出失敗:', error);
            alert('數據匯出失敗，請重試');
        }
    }
}

// 匯入資料
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);

            // 驗證數據格式
            if (!data.books || !Array.isArray(data.books)) {
                throw new Error('無效的備份檔案格式：缺少書籍數據');
            }

            // 支援新版本和舊版本的格式
            const version = data.version || '1.0';
            
            if (confirm(`匯入資料將覆蓋現有資料，確定要繼續嗎？\n\n檔案版本：${version}`)) {
                // 使用新的模組化結構設置數據
                if (window.dataManager) {
                    window.dataManager.setBooks(data.books);
                } else {
                    books = data.books;
                    saveBooks();
                }

                // 設置學生數據（如果存在）
                if (data.students && Array.isArray(data.students)) {
                    if (window.studentManager) {
                        window.studentManager.setStudents(data.students);
                    } else {
                        students = data.students;
                        localStorage.setItem('classReadingTracker_students', JSON.stringify(students));
                        updateStudentSelectors();
                    }
                }

                // 設置用戶設置
                if (data.settings) {
                    if (window.dataManager) {
                        window.dataManager.setUserSettings({ ...userSettings, ...data.settings });
                    } else {
                        userSettings = { ...userSettings, ...data.settings };
                        localStorage.setItem('classReadingTracker_settings', JSON.stringify(userSettings));
                        if (elements.goalTarget) {
                            elements.goalTarget.textContent = userSettings.yearlyGoal;
                        }
                    }
                }

                // 更新UI
                if (window.bookManager) {
                    window.bookManager.renderBooks();
                } else {
                    renderBooks();
                }

                if (window.statsManager) {
                    window.statsManager.updateStats();
                } else {
                    updateStats();
                }

                // 使用新的錯誤處理系統記錄成功事件
                if (window.errorHandler) {
                    window.errorHandler.logEvent({
                        type: 'import',
                        message: '數據匯入成功',
                        context: {
                            booksCount: data.books.length,
                            studentsCount: data.students ? data.students.length : 0,
                            version: version,
                            importDate: new Date().toISOString()
                        },
                        timestamp: new Date().toISOString()
                    });
                }
                
                console.log('數據匯入成功');
                alert('資料匯入成功！');
            }
        } catch (error) {
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.DATA_CORRUPTION,
                    severity: window.errorHandler.ErrorSeverity.HIGH,
                    message: '數據匯入失敗: ' + error.message,
                    context: { action: 'importData', error: error },
                    timestamp: new Date().toISOString()
                });
            } else {
                console.error('數據匯入失敗:', error);
                alert('檔案讀取失敗，請確認檔案格式正確');
            }
        }
    };
    reader.readAsText(file);
}

// 快速更新進度
function quickUpdateProgress(bookId, increment) {
    const book = books.find(b => b.id === bookId);
    if (!book || !book.totalPages) return;

    const newPage = Math.max(0, Math.min(book.totalPages, book.currentPage + increment));
    book.currentPage = newPage;

    // 如果讀完了，自動設為已完成
    if (newPage === book.totalPages && book.status === 'reading') {
        book.status = 'completed';
    }

    book.dateModified = new Date().toISOString();
    saveBooks();
    renderBooks();
    updateStats();
}

// 批量操作
function bulkOperation(operation, bookIds) {
    bookIds.forEach(bookId => {
        const book = books.find(b => b.id === bookId);
        if (!book) return;

        switch (operation) {
            case 'delete':
                books = books.filter(b => b.id !== bookId);
                break;
            case 'mark-completed':
                book.status = 'completed';
                if (book.totalPages > 0) {
                    book.currentPage = book.totalPages;
                }
                break;
            case 'mark-reading':
                book.status = 'reading';
                break;
            case 'mark-to-read':
                book.status = 'to-read';
                book.currentPage = 0;
                break;
        }

        book.dateModified = new Date().toISOString();
    });

    saveBooks();
    renderBooks();
    updateStats();
}

// 鍵盤快捷鍵
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + N: 新增書籍
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openBookModal();
    }

    // Ctrl/Cmd + F: 聚焦搜尋框
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        elements.searchInput.focus();
    }

    // ESC: 關閉模態框
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            closeModal(openModal.id);
        }
    }
});

// 自動儲存提醒
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(() => {
        // 每5分鐘自動提醒備份
        if (books.length > 0) {
            console.log('建議定期備份您的閱讀資料');
        }
    }, 5 * 60 * 1000);
}

// 啟動自動儲存
startAutoSave();

// 頁面卸載前提醒
window.addEventListener('beforeunload', function(e) {
    if (books.length > 0) {
        // 最後一次儲存
        saveBooks();
    }
});

// 添加匯入/匯出按鈕到頁面
function addImportExportButtons() {
    try {
        const headerActions = document.querySelector('.header-actions');
        if (!headerActions) {
            throw new Error('找不到 headerActions 元素');
        }

        // 匯出按鈕
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-secondary';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> 匯出';
        exportBtn.title = '匯出閱讀數據備份';
        exportBtn.onclick = function() {
            exportData();
        };

        // 匯入按鈕
        const importBtn = document.createElement('button');
        importBtn.className = 'btn btn-secondary';
        importBtn.innerHTML = '<i class="fas fa-upload"></i> 匯入';
        importBtn.title = '匯入閱讀數據備份';

        const importInput = document.createElement('input');
        importInput.type = 'file';
        importInput.accept = '.json';
        importInput.style.display = 'none';
        importInput.onchange = importData;

        importBtn.onclick = function() {
            // 重置文件輸入，允許重複選擇同一文件
            importInput.value = '';
            importInput.click();
        };

        // 添加分隔線
        const divider = document.createElement('div');
        divider.className = 'header-divider';
        divider.style.cssText = 'width: 1px; height: 30px; background-color: #ddd; margin: 0 10px;';

        // 添加按鈕到頁面
        headerActions.appendChild(divider);
        headerActions.appendChild(exportBtn);
        headerActions.appendChild(importBtn);
        headerActions.appendChild(importInput);

        // 使用新的錯誤處理系統記錄成功事件
        if (window.errorHandler) {
            window.errorHandler.logEvent({
                type: 'ui',
                message: '匯入/匯出按鈕已添加',
                context: { action: 'addImportExportButtons' },
                timestamp: new Date().toISOString()
            });
        }
        
        console.log('匯入/匯出按鈕已添加');
    } catch (error) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.DOM_ERROR,
                severity: window.errorHandler.ErrorSeverity.LOW,
                message: '添加匯入/匯出按鈕失敗: ' + error.message,
                context: { action: 'addImportExportButtons', error: error },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('添加匯入/匯出按鈕失敗:', error);
        }
    }
}

// 初始化匯入匯出功能
addImportExportButtons();

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>學生選擇器調試測試</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            margin-bottom: 1rem;
        }
        
        button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .info {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 學生選擇器調試測試</h1>
        
        <div class="test-section">
            <h3>測試學生選擇器</h3>
            <select id="studentSelect">
                <option value="">選擇學生</option>
            </select>
            
            <select id="studentId">
                <option value="">請選擇學生</option>
            </select>
            
            <button onclick="testStudentData()">測試學生資料載入</button>
            <button onclick="testElementsInit()">測試元素初始化</button>
            <button onclick="testUpdateSelectors()">測試更新選擇器</button>
            <button onclick="clearDebug()">清除調試信息</button>
        </div>
        
        <div class="test-section">
            <h3>調試信息</h3>
            <div id="debugInfo" class="debug-info">點擊上方按鈕開始測試...</div>
        </div>
    </div>

    <script>
        // 複製主要的學生資料和函數
        const defaultStudents = [
            { id: '01', name: '孔子', fullName: '孔丘' },
            { id: '02', name: '老子', fullName: '李耳' },
            { id: '03', name: '孟子', fullName: '孟軻' },
            { id: '04', name: '莊子', fullName: '莊周' },
            { id: '05', name: '荀子', fullName: '荀況' },
            { id: '06', name: '墨子', fullName: '墨翟' },
            { id: '07', name: '韓非子', fullName: '韓非' },
            { id: '08', name: '司馬遷', fullName: '司馬遷' },
            { id: '09', name: '諸葛亮', fullName: '諸葛亮' },
            { id: '10', name: '李白', fullName: '李太白' }
        ];
        
        let students = JSON.parse(localStorage.getItem('classReadingTracker_students')) || [...defaultStudents];
        let elements = {};
        
        function log(message, type = 'info') {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            debugInfo.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function clearDebug() {
            document.getElementById('debugInfo').innerHTML = '';
        }
        
        function initializeElements() {
            log('初始化 DOM 元素...', 'info');
            
            elements = {
                studentSelect: document.getElementById('studentSelect'),
                studentId: document.getElementById('studentId')
            };
            
            log('studentSelect 元素: ' + (elements.studentSelect ? '✅ 找到' : '❌ 未找到'), 
                elements.studentSelect ? 'success' : 'error');
            log('studentId 元素: ' + (elements.studentId ? '✅ 找到' : '❌ 未找到'), 
                elements.studentId ? 'success' : 'error');
            
            log('DOM 元素初始化完成', 'success');
        }
        
        function updateStudentSelectors() {
            log('正在更新學生選擇器...', 'info');
            log('學生數量: ' + students.length, 'info');
            
            // 檢查元素是否存在
            if (!elements.studentSelect) {
                log('❌ 找不到 studentSelect 元素', 'error');
                return;
            }
            if (!elements.studentId) {
                log('❌ 找不到 studentId 元素', 'error');
                return;
            }
            
            // 清空現有選項
            elements.studentSelect.innerHTML = '<option value="">🎓 選擇學生</option>';
            elements.studentId.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
            
            log('已清空選擇器選項', 'info');
            
            // 重新添加學生選項
            students.forEach((student, index) => {
                // 頂部學生選擇器
                const option1 = document.createElement('option');
                option1.value = student.id;
                option1.textContent = `${student.id}號 ${student.name}`;
                elements.studentSelect.appendChild(option1);
                
                // 表單中的學生選擇器
                const option2 = document.createElement('option');
                option2.value = student.id;
                option2.textContent = `${student.id}號 ${student.name}（${student.fullName}）`;
                elements.studentId.appendChild(option2);
                
                if (index < 3) { // 只顯示前3個學生的詳細信息
                    log(`添加學生: ${student.id}號 ${student.name}`, 'success');
                }
            });
            
            log(`✅ 學生選擇器更新完成，共添加 ${students.length} 位學生`, 'success');
            
            // 驗證選項數量
            const select1Options = elements.studentSelect.options.length;
            const select2Options = elements.studentId.options.length;
            log(`studentSelect 選項數量: ${select1Options}`, 'info');
            log(`studentId 選項數量: ${select2Options}`, 'info');
        }
        
        function testStudentData() {
            log('=== 測試學生資料載入 ===', 'info');
            log('學生資料來源: ' + (localStorage.getItem('classReadingTracker_students') ? 'localStorage' : '預設資料'), 'info');
            log('學生數量: ' + students.length, 'info');
            log('前5位學生:', 'info');
            students.slice(0, 5).forEach(student => {
                log(`  ${student.id}號 ${student.name} (${student.fullName})`, 'info');
            });
        }
        
        function testElementsInit() {
            log('=== 測試元素初始化 ===', 'info');
            initializeElements();
        }
        
        function testUpdateSelectors() {
            log('=== 測試更新選擇器 ===', 'info');
            if (Object.keys(elements).length === 0) {
                log('元素未初始化，先初始化...', 'info');
                initializeElements();
            }
            updateStudentSelectors();
        }
        
        // 頁面載入時自動測試
        window.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成', 'success');
            setTimeout(() => {
                testElementsInit();
                testStudentData();
                testUpdateSelectors();
            }, 100);
        });
    </script>
</body>
</html>

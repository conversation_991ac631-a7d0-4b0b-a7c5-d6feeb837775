// UI管理模組 - 負責UI元素管理和事件處理

// DOM 元素
let elements = {};

// 初始化UI管理器
function initUIManager() {
    console.log('初始化UI管理器...');
    initializeElements();
    setupUIEventListeners();
    updateStudentSelectors();
    initializeFontSize();
}

// 初始化 DOM 元素
function initializeElements() {
    console.log('初始化 DOM 元素...');

    elements = {
        // 按鈕
        addBookBtn: document.getElementById('addBookBtn'),
        batchAddBtn: document.getElementById('batchAddBtn'),
        downloadBtn: document.getElementById('downloadBtn'),
        setGoalBtn: document.getElementById('setGoalBtn'),
        manageStudentsBtn: document.getElementById('manageStudentsBtn'),
        closeModal: document.getElementById('closeModal'),
        closeGoalModal: document.getElementById('closeGoalModal'),
        closeDetailModal: document.getElementById('closeDetailModal'),
        closeStudentsModal: document.getElementById('closeStudentsModal'),
        closeBatchAddModal: document.getElementById('closeBatchAddModal'),
        cancelBtn: document.getElementById('cancelBtn'),
        cancelGoalBtn: document.getElementById('cancelGoalBtn'),
        cancelBatchAddBtn: document.getElementById('cancelBatchAddBtn'),
        processBatchAddBtn: document.getElementById('processBatchAddBtn'),
        showAllStudents: document.getElementById('showAllStudents'),
        showTopReaders: document.getElementById('showTopReaders'),
        resetStudentsBtn: document.getElementById('resetStudentsBtn'),
        saveStudentsBtn: document.getElementById('saveStudentsBtn'),
        
        // 選擇器
        fontSizeSelect: document.getElementById('fontSizeSelect'),

        // 模態框
        bookModal: document.getElementById('bookModal'),
        batchAddModal: document.getElementById('batchAddModal'),
        goalModal: document.getElementById('goalModal'),
        bookDetailModal: document.getElementById('bookDetailModal'),
        studentsModal: document.getElementById('studentsModal'),

        // 表單
        bookForm: document.getElementById('bookForm'),
        goalForm: document.getElementById('goalForm'),

        // 輸入欄位
        searchInput: document.getElementById('searchInput'),
        sortSelect: document.getElementById('sortSelect'),
        studentSelect: document.getElementById('studentSelect'),
        studentId: document.getElementById('studentId'),

        // 容器
        booksContainer: document.getElementById('booksContainer'),
        emptyState: document.getElementById('emptyState'),

        // 統計
        totalBooks: document.getElementById('totalBooks'),
        completedBooks: document.getElementById('completedBooks'),
        readingBooks: document.getElementById('readingBooks'),
        totalPages: document.getElementById('totalPages'),
        activeStudents: document.getElementById('activeStudents'),
        goalProgress: document.getElementById('goalProgress'),
        goalTarget: document.getElementById('goalTarget'),
        goalProgressBar: document.getElementById('goalProgressBar'),
        avgPerStudent: document.getElementById('avgPerStudent'),

        // 其他
        modalTitle: document.getElementById('modalTitle'),
        ratingInput: document.getElementById('ratingInput'),
        studentsGrid: document.getElementById('studentsGrid')
    };

    // 檢查關鍵元素
    console.log('studentSelect 元素:', elements.studentSelect);
    console.log('studentId 元素:', elements.studentId);
    console.log('manageStudentsBtn 元素:', elements.manageStudentsBtn);

    console.log('DOM 元素初始化完成');
}

// 設置UI事件監聽器
function setupUIEventListeners() {
    console.log('設置UI事件監聽器...');

    // 檢查關鍵元素是否存在
    if (!elements.manageStudentsBtn) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '找不到 manageStudentsBtn 元素',
                element: 'manageStudentsBtn',
                context: { action: 'setupUIEventListeners' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('找不到 manageStudentsBtn 元素');
        }
    } else {
        console.log('找到 manageStudentsBtn 元素');
    }

    // 模態框事件
    elements.addBookBtn?.addEventListener('click', () => {
        if (window.bookManager) {
            window.bookManager.openBookModal();
        }
    });

    // 批次新增按鈕
    elements.batchAddBtn?.addEventListener('click', () => {
        if (window.bookManager) {
            window.bookManager.openBatchAddModal();
        }
    });

    // 下載按鈕
    elements.downloadBtn?.addEventListener('click', () => {
        if (window.bookManager) {
            window.bookManager.downloadBooksCSV();
        }
    });
    
    elements.setGoalBtn?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.openGoalModal();
        }
    });

    if (elements.manageStudentsBtn) {
        elements.manageStudentsBtn.addEventListener('click', () => {
            console.log('管理學生名單按鈕被點擊');
            if (window.modalManager) {
                window.modalManager.openStudentsModal();
            }
        });
    }

    elements.closeModal?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('bookModal');
        }
    });
    
    elements.closeGoalModal?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('goalModal');
        }
    });
    
    elements.closeDetailModal?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('bookDetailModal');
        }
    });
    
    elements.closeStudentsModal?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('studentsModal');
        }
    });
    
    elements.cancelBtn?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('bookModal');
        }
    });
    
    elements.cancelGoalBtn?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('goalModal');
        }
    });

    // 批次新增模態框事件
    elements.closeBatchAddModal?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('batchAddModal');
        }
    });

    elements.cancelBatchAddBtn?.addEventListener('click', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('batchAddModal');
        }
    });

    elements.processBatchAddBtn?.addEventListener('click', () => {
        if (window.bookManager) {
            window.bookManager.handleBatchAddSubmit();
        }
    });
    
    // 表單提交
    if (elements.bookForm && window.bookManager) {
        elements.bookForm.addEventListener('submit', window.bookManager.handleBookSubmit);
    }
    
    if (elements.goalForm && window.modalManager) {
        elements.goalForm.addEventListener('submit', window.modalManager.handleGoalSubmit);
    }

    // 評分輸入
    if (elements.ratingInput && window.bookManager) {
        elements.ratingInput.addEventListener('click', window.bookManager.handleRatingClick);
    }
    
    // 學生管理事件
    if (elements.resetStudentsBtn && window.studentManager) {
        elements.resetStudentsBtn.addEventListener('click', () => {
            if (window.studentManager.resetStudents()) {
                updateStudentSelectors();
                if (window.bookManager) {
                    window.bookManager.renderBooks();
                }
            }
        });
    }
    
    if (elements.saveStudentsBtn && elements.studentsGrid && window.studentManager) {
        elements.saveStudentsBtn.addEventListener('click', () => {
            if (window.studentManager.saveStudentsFromGrid(elements.studentsGrid)) {
                if (window.modalManager) {
                    window.modalManager.closeModal('studentsModal');
                }
                if (window.bookManager) {
                    window.bookManager.renderBooks();
                }
            }
        });
    }
    
    // 點擊模態框外部關閉
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal && window.modalManager) {
                window.modalManager.closeModal(modal.id);
            }
        });
    });

    // 批次新增模態框標籤頁切換
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tabId = e.target.dataset.tab;
            if (tabId && window.bookManager) {
                window.bookManager.switchBatchAddTab(tabId);
            }
        });
    });

    // CSV 檔案預覽
    const csvFileInput = document.getElementById('csvFileInput');
    if (csvFileInput && window.bookManager) {
        csvFileInput.addEventListener('change', window.bookManager.previewCSVFile);
    }

    // 字體大小選擇器
    if (elements.fontSizeSelect) {
        // 先移除可能存在的舊事件監聽器
        elements.fontSizeSelect.removeEventListener('change', handleFontSizeChange);
        // 添加新的事件監聽器
        elements.fontSizeSelect.addEventListener('change', handleFontSizeChange);
        console.log('字體大小選擇器事件監聽器已設置');
    } else {
        // 使用新的錯誤處理系統記錄錯誤
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '找不到 fontSizeSelect 元素',
                element: 'fontSizeSelect',
                context: { action: 'setupUIEventListeners' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('找不到 fontSizeSelect 元素');
        }
    }

    // 監聽自定義事件
    setupCustomEventListeners();
}

// 設置自定義事件監聽器
function setupCustomEventListeners() {
    // 監聽學生資料更新事件
    window.addEventListener('studentsUpdated', () => {
        console.log('收到學生資料更新事件，更新選擇器');
        updateStudentSelectors();
    });

    // 監聽學生資料變更事件
    window.addEventListener('studentsDataChanged', () => {
        console.log('收到學生資料變更事件，重新渲染書籍');
        if (window.bookManager) {
            window.bookManager.renderBooks();
        }
    });

    // 監聽書籍保存事件
    window.addEventListener('bookSaved', () => {
        console.log('收到書籍保存事件，更新統計');
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
    });

    // 監聽書籍刪除事件
    window.addEventListener('bookDeleted', () => {
        console.log('收到書籍刪除事件，更新統計');
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
    });

    // 監聽書籍更新事件
    window.addEventListener('bookUpdated', () => {
        console.log('收到書籍更新事件，更新統計');
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
    });

    // 監聽批量書籍更新事件
    window.addEventListener('booksBulkUpdated', () => {
        console.log('收到批量書籍更新事件，更新統計');
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
    });

    // 監聽數據匯入事件
    window.addEventListener('dataImported', (event) => {
        console.log('收到數據匯入事件，更新UI');
        const { books, userSettings } = event.detail;
        
        // 更新目標顯示
        if (elements.goalTarget) {
            elements.goalTarget.textContent = userSettings.yearlyGoal;
        }
        
        if (elements.avgPerStudent) {
            elements.avgPerStudent.textContent = Math.round(userSettings.yearlyGoal / 27);
        }
        
        // 更新書籍列表和統計
        if (window.bookManager) {
            window.bookManager.renderBooks();
        }
        
        if (window.statsManager) {
            window.statsManager.updateStats();
        }
    });

    // 監聽打開書籍模態框事件
    window.addEventListener('openBookModal', (event) => {
        const { book, editingBookId } = event.detail;
        openBookModalHandler(book, editingBookId);
    });

    // 監聽顯示書籍詳情事件
    window.addEventListener('showBookDetail', (event) => {
        const { detailContent } = event.detail;
        showBookDetailHandler(detailContent);
    });

    // 監聽關閉書籍模態框事件
    window.addEventListener('closeBookModal', () => {
        if (window.modalManager) {
            window.modalManager.closeModal('bookModal');
        }
    });

    // 監聽打開批次新增模態框事件
    window.addEventListener('openBatchAddModal', () => {
        if (window.modalManager) {
            window.modalManager.showModal('batchAddModal');
        }
    });
}

// 打開書籍模態框處理器
function openBookModalHandler(book, editingBookId) {
    if (!elements.modalTitle || !elements.studentId) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '必要的模態框元素不存在',
                element: 'modalTitle 或 studentId',
                context: { action: 'openBookModalHandler', book, editingBookId },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('必要的模態框元素不存在');
        }
        return;
    }

    elements.modalTitle.textContent = book ? '編輯閱讀記錄' : '新增閱讀記錄';

    if (book) {
        // 填入現有資料
        elements.studentId.value = book.studentId || '';
        document.getElementById('bookTitle').value = book.title || '';
        document.getElementById('bookAuthor').value = book.author || '';
        document.getElementById('totalPages').value = book.totalPages || '';
        document.getElementById('currentPage').value = book.currentPage || 0;
        document.getElementById('bookStatus').value = book.status || 'to-read';
        document.getElementById('bookCover').value = book.coverImage || '';
        document.getElementById('bookTags').value = book.tags ? book.tags.join(', ') : '';
        document.getElementById('bookNotes').value = book.notes || '';

        // 設定評分
        if (window.bookManager) {
            window.bookManager.setRating(book.rating || 0);
        }
    } else {
        // 清空表單
        if (elements.bookForm) {
            elements.bookForm.reset();
        }
        if (window.bookManager) {
            window.bookManager.setRating(0);
        }

        // 如果有選中的學生，預設選擇
        if (window.bookManager && window.bookManager.selectedStudent) {
            elements.studentId.value = window.bookManager.selectedStudent;
        }
    }

    if (window.modalManager) {
        window.modalManager.showModal('bookModal');
    }
}

// 顯示書籍詳情處理器
function showBookDetailHandler(detailContent) {
    const bookDetailContent = document.getElementById('bookDetailContent');
    if (bookDetailContent) {
        bookDetailContent.innerHTML = detailContent;
        if (window.modalManager) {
            window.modalManager.showModal('bookDetailModal');
        }
    }
}

// 更新學生選擇器
function updateStudentSelectors() {
    console.log('=== 開始更新學生選擇器 ===');
    
    const students = window.studentManager ? window.studentManager.getStudents() : [];
    console.log('學生數據:', students.length, '位學生');
    console.log('前3位學生:', students.slice(0, 3));

    // 檢查元素是否存在
    console.log('檢查 DOM 元素:');
    console.log('  studentSelect:', elements.studentSelect);
    console.log('  studentId:', elements.studentId);

    if (!elements.studentSelect) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '找不到 studentSelect 元素',
                element: 'studentSelect',
                context: { action: 'updateStudentSelectors' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ 找不到 studentSelect 元素');
        }
        // 嘗試重新獲取元素
        elements.studentSelect = document.getElementById('studentSelect');
        console.log('重新獲取 studentSelect:', elements.studentSelect);
    }
    if (!elements.studentId) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '找不到 studentId 元素',
                element: 'studentId',
                context: { action: 'updateStudentSelectors' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ 找不到 studentId 元素');
        }
        // 嘗試重新獲取元素
        elements.studentId = document.getElementById('studentId');
        console.log('重新獲取 studentId:', elements.studentId);
    }

    // 如果還是找不到元素，直接返回
    if (!elements.studentSelect || !elements.studentId) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.CRITICAL,
                message: '無法找到必要的選擇器元素，停止更新',
                element: 'studentSelect 或 studentId',
                context: { action: 'updateStudentSelectors' },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ 無法找到必要的選擇器元素，停止更新');
        }
        return;
    }

    console.log('✅ 元素檢查通過，開始更新選項');

    // 清空現有選項
    elements.studentSelect.innerHTML = '<option value="">🎓 選擇學生</option>';
    elements.studentId.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
    console.log('已清空現有選項');

    // 檢查學生資料
    if (students.length === 0) {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.DATA_CORRUPTION,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: '學生資料為空，無法添加選項',
                context: { action: 'updateStudentSelectors', studentsCount: students.length },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ 學生資料為空，無法添加選項');
        }
        return;
    }

    console.log('開始添加學生選項...');
    // 重新添加學生選項
    students.forEach((student, index) => {
        // 頂部學生選擇器
        const option1 = document.createElement('option');
        option1.value = student.id;
        option1.textContent = `${student.id}號 ${student.name}`;
        elements.studentSelect.appendChild(option1);

        // 表單中的學生選擇器
        const option2 = document.createElement('option');
        option2.value = student.id;
        option2.textContent = `${student.id}號 ${student.name}`;
        elements.studentId.appendChild(option2);

        // 只記錄前3個學生的添加過程
        if (index < 3) {
            console.log(`添加學生 ${index + 1}: ${student.id}號 ${student.name}`);
        }
    });

    // 驗證結果
    console.log('驗證更新結果:');
    console.log('  studentSelect 選項數:', elements.studentSelect.options.length);
    console.log('  studentId 選項數:', elements.studentId.options.length);

    if (elements.studentSelect.options.length > 1) {
        console.log('✅ studentSelect 更新成功');
    } else {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: 'studentSelect 更新失敗',
                element: 'studentSelect',
                context: { action: 'updateStudentSelectors', optionsCount: elements.studentSelect.options.length },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ studentSelect 更新失敗');
        }
    }

    if (elements.studentId.options.length > 1) {
        console.log('✅ studentId 更新成功');
    } else {
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.HIGH,
                message: 'studentId 更新失敗',
                element: 'studentId',
                context: { action: 'updateStudentSelectors', optionsCount: elements.studentId.options.length },
                timestamp: new Date().toISOString()
            });
        } else {
            console.error('❌ studentId 更新失敗');
        }
    }

    console.log('=== 學生選擇器更新完成 ===');
}

// 初始化學生選單
function initializeStudentSelectors() {
    updateStudentSelectors();
}

// 強制更新學生選擇器的測試函數
window.forceUpdateSelectors = function() {
    console.log('強制更新學生選擇器...');
    updateStudentSelectors();
    console.log('更新完成，請檢查選擇器');
};

// 處理字體大小變更
function handleFontSizeChange(e) {
    try {
        const fontSize = e.target.value;
        console.log('字體大小變更:', fontSize);
        
        // 驗證字體大小值
        const validFontSizes = ['small', 'medium', 'large'];
        if (!validFontSizes.includes(fontSize)) {
            throw new Error(`無效的字體大小值: ${fontSize}`);
        }
        
        // 移除舊的字體大小類別
        document.body.classList.remove('font-size-small', 'font-size-medium', 'font-size-large');
        
        // 添加新的字體大小類別
        document.body.classList.add(`font-size-${fontSize}`);
        console.log('字體大小類別已添加:', `font-size-${fontSize}`);
        
        // 儲存字體大小設定到 localStorage
        try {
            localStorage.setItem('classReadingTracker_fontSize', fontSize);
            console.log('字體大小設定已儲存到 localStorage');
        } catch (storageError) {
            console.error('儲存字體大小設定到 localStorage 失敗:', storageError);
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.STORAGE,
                    severity: window.errorHandler.ErrorSeverity.LOW,
                    message: '儲存字體大小設定失敗: ' + storageError.message,
                    context: { fontSize, action: 'saveFontSize' },
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        // 使用新的錯誤處理系統記錄事件
        if (window.errorHandler) {
            window.errorHandler.logEvent({
                type: 'ui',
                message: '字體大小已更改',
                context: { fontSize: fontSize },
                timestamp: new Date().toISOString()
            });
        }
        
        console.log('字體大小變更處理完成');
    } catch (error) {
        console.error('處理字體大小變更時發生錯誤:', error);
        
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '字體大小變更失敗: ' + error.message,
                element: 'fontSizeSelect',
                context: {
                    action: 'handleFontSizeChange',
                    fontSize: e.target.value,
                    error: error
                },
                timestamp: new Date().toISOString()
            });
        } else {
            // 顯示用戶友好的錯誤訊息
            alert('字體大小調整失敗，請重新整理頁面後再試。');
        }
    }
}

// 初始化字體大小設定
function initializeFontSize() {
    try {
        console.log('開始初始化字體大小設定...');
        
        let savedFontSize = null;
        
        // 嘗試從 localStorage 讀取字體大小設定
        try {
            savedFontSize = localStorage.getItem('classReadingTracker_fontSize');
            console.log('從 localStorage 讀取的字體大小:', savedFontSize);
        } catch (storageError) {
            console.error('從 localStorage 讀取字體大小設定失敗:', storageError);
            // 使用新的錯誤處理系統
            if (window.errorHandler) {
                window.errorHandler.handleError({
                    type: window.errorHandler.ErrorTypes.STORAGE,
                    severity: window.errorHandler.ErrorSeverity.LOW,
                    message: '讀取字體大小設定失敗: ' + storageError.message,
                    context: { action: 'loadFontSize' },
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        // 移除所有字體大小類別
        document.body.classList.remove('font-size-small', 'font-size-medium', 'font-size-large');
        console.log('已移除所有字體大小類別');
        
        if (savedFontSize) {
            // 驗證儲存的字體大小值
            const validFontSizes = ['small', 'medium', 'large'];
            if (validFontSizes.includes(savedFontSize)) {
                // 添加儲存的字體大小類別
                document.body.classList.add(`font-size-${savedFontSize}`);
                console.log('已添加儲存的字體大小類別:', `font-size-${savedFontSize}`);
                
                // 更新選擇器的值
                if (elements.fontSizeSelect) {
                    elements.fontSizeSelect.value = savedFontSize;
                    console.log('已更新字體大小選擇器的值:', savedFontSize);
                } else {
                    console.warn('fontSizeSelect 元素不存在，無法更新選擇器值');
                }
            } else {
                console.warn('儲存的字體大小值無效:', savedFontSize, '使用預設值');
                // 使用預設值
                document.body.classList.add('font-size-medium');
                if (elements.fontSizeSelect) {
                    elements.fontSizeSelect.value = 'medium';
                }
            }
        } else {
            // 預設使用中字體
            document.body.classList.add('font-size-medium');
            console.log('使用預設字體大小: medium');
            if (elements.fontSizeSelect) {
                elements.fontSizeSelect.value = 'medium';
            }
        }
        
        console.log('字體大小設定初始化完成');
    } catch (error) {
        console.error('初始化字體大小設定時發生錯誤:', error);
        
        // 使用新的錯誤處理系統
        if (window.errorHandler) {
            window.errorHandler.handleError({
                type: window.errorHandler.ErrorTypes.UI_ERROR,
                severity: window.errorHandler.ErrorSeverity.MEDIUM,
                message: '初始化字體大小設定失敗: ' + error.message,
                context: { action: 'initializeFontSize', error: error },
                timestamp: new Date().toISOString()
            });
        }
        
        // 確保至少有預設的字體大小
        document.body.classList.remove('font-size-small', 'font-size-medium', 'font-size-large');
        document.body.classList.add('font-size-medium');
    }
}

// 導出函數供其他模組使用
window.uiManager = {
    initUIManager,
    initializeElements,
    updateStudentSelectors,
    initializeStudentSelectors,
    initializeFontSize,
    handleFontSizeChange,
    getElements: () => elements
};
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字體大小調整功能測試</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 2px solid #ffb6c1;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.8);
        }
        
        .test-section h3 {
            color: #ff6b9d;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b9d;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .test-btn:hover {
            background: rgba(255, 182, 193, 0.2);
            transform: translateY(-2px);
        }
        
        .test-content {
            padding: 1rem;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .sample-book-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
            margin-bottom: 1rem;
            border: 2px solid rgba(255, 182, 193, 0.3);
        }
        
        .sample-sidebar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
            border: 2px solid rgba(255, 182, 193, 0.3);
        }
        
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }
        
        .log-entry.error {
            color: #ef4444;
        }
        
        .log-entry.success {
            color: #10b981;
        }
        
        .log-entry.info {
            color: #3b82f6;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-indicator.success {
            background: #10b981;
        }
        
        .status-indicator.error {
            background: #ef4444;
        }
        
        .status-indicator.info {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #ff6b9d; margin-bottom: 2rem;">
            <i class="fas fa-text-height"></i> 字體大小調整功能測試
        </h1>
        
        <!-- 測試控制區 -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> 測試控制</h3>
            <div class="test-controls">
                <select id="fontSizeSelect" class="font-size-selector">
                    <option value="small">小字體</option>
                    <option value="medium" selected>中字體</option>
                    <option value="large">大字體</option>
                </select>
                <button class="test-btn" onclick="testFontSizeChange()">
                    <i class="fas fa-play"></i> 測試字體變更
                </button>
                <button class="test-btn" onclick="testLocalStorage()">
                    <i class="fas fa-database"></i> 測試本地存儲
                </button>
                <button class="test-btn" onclick="testErrorHandling()">
                    <i class="fas fa-bug"></i> 測試錯誤處理
                </button>
                <button class="test-btn" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> 清除日誌
                </button>
            </div>
        </div>
        
        <!-- 測試內容區 -->
        <div class="test-section">
            <h3><i class="fas fa-eye"></i> 測試內容</h3>
            <div class="test-content">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- 樣本書籍卡片 -->
                    <div>
                        <h4 style="color: #ff6b9d; margin-bottom: 1rem;">書籍卡片樣本</h4>
                        <div class="sample-book-card">
                            <div class="book-header">
                                <div class="book-info">
                                    <h3>紅樓夢</h3>
                                    <div class="author">曹雪芹</div>
                                    <div class="student-name">
                                        <span class="student-number">01</span>
                                        孔子
                                    </div>
                                </div>
                                <div class="book-status status-reading">閱讀中</div>
                            </div>
                            <div class="book-progress">
                                <div class="progress-text">
                                    <span>進度</span>
                                    <span>350 / 1200 頁</span>
                                </div>
                                <div class="progress-bar-book">
                                    <div class="progress-fill-book" style="width: 29%"></div>
                                </div>
                            </div>
                            <div class="book-tags">
                                <span class="tag">古典文學</span>
                                <span class="tag">小說</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 樣本側邊欄 -->
                    <div>
                        <h4 style="color: #ff6b9d; margin-bottom: 1rem;">側邊欄樣本</h4>
                        <div class="sample-sidebar">
                            <h3>班級閱讀統計</h3>
                            <div class="stat-item">
                                <span class="stat-label">全班總書籍</span>
                                <span class="stat-value">42</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">已完成</span>
                                <span class="stat-value">15</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">閱讀中</span>
                                <span class="stat-value">18</span>
                            </div>
                            <div class="filter-buttons">
                                <div class="filter-btn active">全部記錄</div>
                                <div class="filter-btn">待讀</div>
                                <div class="filter-btn">閱讀中</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日誌輸出區 -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> 測試日誌</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry info">
                    <span class="status-indicator info"></span>
                    測試頁面已載入，等待測試...
                </div>
            </div>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="error-handler.js"></script>
    <script src="ui-manager.js"></script>
    
    <script>
        // 測試日誌記錄器
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const statusIndicator = document.createElement('span');
            statusIndicator.className = `status-indicator ${type}`;
            
            logEntry.appendChild(statusIndicator);
            logEntry.appendChild(document.createTextNode(`[${timestamp}] ${message}`));
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同時輸出到控制台
            console.log(`[測試] ${message}`);
        }
        
        // 清除日誌
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '';
            log('日誌已清除', 'info');
        }
        
        // 測試字體變更功能
        function testFontSizeChange() {
            log('開始測試字體變更功能...', 'info');
            
            try {
                // 初始化錯誤處理器
                if (window.initErrorHandler) {
                    window.initErrorHandler();
                    log('錯誤處理器已初始化', 'success');
                }
                
                // 初始化UI管理器
                if (window.initUIManager) {
                    window.initUIManager();
                    log('UI管理器已初始化', 'success');
                }
                
                // 測試字體大小選擇器
                const fontSizeSelect = document.getElementById('fontSizeSelect');
                if (!fontSizeSelect) {
                    throw new Error('找不到字體大小選擇器元素');
                }
                
                log('找到字體大小選擇器元素', 'success');
                
                // 測試每個字體大小選項
                const fontSizes = ['small', 'medium', 'large'];
                fontSizes.forEach(size => {
                    log(`測試字體大小: ${size}`, 'info');
                    
                    // 設置選擇器值
                    fontSizeSelect.value = size;
                    
                    // 觸發變更事件
                    const event = new Event('change', { bubbles: true });
                    fontSizeSelect.dispatchEvent(event);
                    
                    // 檢查body是否有對應的class
                    const hasClass = document.body.classList.contains(`font-size-${size}`);
                    if (hasClass) {
                        log(`字體大小 ${size} 應用成功`, 'success');
                    } else {
                        log(`字體大小 ${size} 應用失敗`, 'error');
                    }
                    
                    // 等待一下讓變更生效
                    setTimeout(() => {
                        // 檢查localStorage中的值
                        const savedValue = localStorage.getItem('classReadingTracker_fontSize');
                        if (savedValue === size) {
                            log(`字體大小 ${size} 已儲存到localStorage`, 'success');
                        } else {
                            log(`字體大小 ${size} 儲存到localStorage失敗`, 'error');
                        }
                    }, 100);
                });
                
                log('字體變更功能測試完成', 'success');
                
            } catch (error) {
                log(`字體變更功能測試失敗: ${error.message}`, 'error');
                console.error('字體變更功能測試失敗:', error);
            }
        }
        
        // 測試本地存儲功能
        function testLocalStorage() {
            log('開始測試本地存儲功能...', 'info');
            
            try {
                // 測試儲存功能
                const testSizes = ['small', 'medium', 'large'];
                testSizes.forEach(size => {
                    localStorage.setItem('classReadingTracker_fontSize', size);
                    const retrieved = localStorage.getItem('classReadingTracker_fontSize');
                    if (retrieved === size) {
                        log(`本地存儲測試 (${size}): 成功`, 'success');
                    } else {
                        log(`本地存儲測試 (${size}): 失敗`, 'error');
                    }
                });
                
                // 測試清除功能
                localStorage.removeItem('classReadingTracker_fontSize');
                const afterRemove = localStorage.getItem('classReadingTracker_fontSize');
                if (afterRemove === null) {
                    log('本地存儲清除測試: 成功', 'success');
                } else {
                    log('本地存儲清除測試: 失敗', 'error');
                }
                
                log('本地存儲功能測試完成', 'success');
                
            } catch (error) {
                log(`本地存儲功能測試失敗: ${error.message}`, 'error');
                console.error('本地存儲功能測試失敗:', error);
            }
        }
        
        // 測試錯誤處理功能
        function testErrorHandling() {
            log('開始測試錯誤處理功能...', 'info');
            
            try {
                // 測試無效的字體大小值
                const fontSizeSelect = document.getElementById('fontSizeSelect');
                if (fontSizeSelect) {
                    // 嘗試設置無效值
                    fontSizeSelect.value = 'invalid';
                    const event = new Event('change', { bubbles: true });
                    fontSizeSelect.dispatchEvent(event);
                    
                    // 檢查是否沒有添加無效的class
                    const hasInvalidClass = document.body.classList.contains('font-size-invalid');
                    if (!hasInvalidClass) {
                        log('無效字體大小值處理: 成功', 'success');
                    } else {
                        log('無效字體大小值處理: 失敗', 'error');
                    }
                }
                
                // 測試localStorage錯誤處理
                // 暫時禁用localStorage
                const originalSetItem = localStorage.setItem;
                localStorage.setItem = function() {
                    throw new Error('模擬localStorage錯誤');
                };
                
                try {
                    localStorage.setItem('classReadingTracker_fontSize', 'test');
                    log('localStorage錯誤處理: 失敗 (未拋出錯誤)', 'error');
                } catch (storageError) {
                    log('localStorage錯誤處理: 成功 (正確捕獲錯誤)', 'success');
                }
                
                // 恢復localStorage
                localStorage.setItem = originalSetItem;
                
                log('錯誤處理功能測試完成', 'success');
                
            } catch (error) {
                log(`錯誤處理功能測試失敗: ${error.message}`, 'error');
                console.error('錯誤處理功能測試失敗:', error);
            }
        }
        
        // 頁面載入完成後自動執行基本測試
        document.addEventListener('DOMContentLoaded', function() {
            log('測試頁面載入完成', 'success');
            
            // 檢查必要的全局對象
            if (window.errorHandler) {
                log('錯誤處理器已載入', 'success');
            } else {
                log('錯誤處理器未載入', 'error');
            }
            
            if (window.uiManager) {
                log('UI管理器已載入', 'success');
            } else {
                log('UI管理器未載入', 'error');
            }
            
            // 檢查字體大小選擇器
            const fontSizeSelect = document.getElementById('fontSizeSelect');
            if (fontSizeSelect) {
                log('字體大小選擇器元素存在', 'success');
            } else {
                log('字體大小選擇器元素不存在', 'error');
            }
            
            // 檢查CSS類別定義
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            
            // 測試小字體
            testElement.className = 'font-size-small-test';
            const smallStyle = window.getComputedStyle(testElement);
            log('小字體CSS類別已定義', 'info');
            
            // 測試中字體
            testElement.className = 'font-size-medium-test';
            const mediumStyle = window.getComputedStyle(testElement);
            log('中字體CSS類別已定義', 'info');
            
            // 測試大字體
            testElement.className = 'font-size-large-test';
            const largeStyle = window.getComputedStyle(testElement);
            log('大字體CSS類別已定義', 'info');
            
            // 清理測試元素
            testElement.remove();
            
            log('基本檢查完成', 'success');
        });
    </script>
</body>
</html>
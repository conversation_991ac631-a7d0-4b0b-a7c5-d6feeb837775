<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>學生名單測試頁面</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff9a9e 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(255, 182, 193, 0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ffb6c1;
            border-radius: 15px;
            font-size: 1rem;
            margin: 0.5rem 0;
        }
        
        button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎓 學生名單測試頁面</h1>
        
        <div class="test-section">
            <h3>測試 1：基本學生選擇器</h3>
            <select id="testSelect1">
                <option value="">載入中...</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>測試 2：表單學生選擇器</h3>
            <select id="testSelect2">
                <option value="">載入中...</option>
            </select>
        </div>
        
        <div class="test-section">
            <h3>測試控制</h3>
            <button onclick="testStudentData()">測試學生資料</button>
            <button onclick="testSelectors()">測試選擇器</button>
            <button onclick="clearDebug()">清除調試信息</button>
        </div>
        
        <div class="test-section">
            <h3>調試信息</h3>
            <div id="debugInfo" class="debug-info">點擊上方按鈕開始測試...</div>
        </div>
    </div>

    <script>
        // 預設學生資料
        const defaultStudents = [
            { id: '01', name: '孔子', fullName: '孔丘' },
            { id: '02', name: '老子', fullName: '李耳' },
            { id: '03', name: '孟子', fullName: '孟軻' },
            { id: '04', name: '莊子', fullName: '莊周' },
            { id: '05', name: '荀子', fullName: '荀況' },
            { id: '06', name: '墨子', fullName: '墨翟' },
            { id: '07', name: '韓非子', fullName: '韓非' },
            { id: '08', name: '司馬遷', fullName: '司馬遷' },
            { id: '09', name: '諸葛亮', fullName: '諸葛亮' },
            { id: '10', name: '李白', fullName: '李太白' },
            { id: '11', name: '杜甫', fullName: '杜子美' },
            { id: '12', name: '白居易', fullName: '白樂天' },
            { id: '13', name: '蘇軾', fullName: '蘇東坡' },
            { id: '14', name: '李清照', fullName: '李清照' },
            { id: '15', name: '辛棄疾', fullName: '辛幼安' },
            { id: '16', name: '朱熹', fullName: '朱元晦' },
            { id: '17', name: '王陽明', fullName: '王守仁' },
            { id: '18', name: '曹雪芹', fullName: '曹霑' },
            { id: '19', name: '吳承恩', fullName: '吳承恩' },
            { id: '20', name: '施耐庵', fullName: '施耐庵' },
            { id: '21', name: '羅貫中', fullName: '羅本' },
            { id: '22', name: '關漢卿', fullName: '關漢卿' },
            { id: '23', name: '馬致遠', fullName: '馬致遠' },
            { id: '24', name: '鄭板橋', fullName: '鄭燮' },
            { id: '25', name: '紀曉嵐', fullName: '紀昀' },
            { id: '26', name: '林則徐', fullName: '林則徐' },
            { id: '27', name: '梁啟超', fullName: '梁啟超' }
        ];

        let students = [...defaultStudents];
        
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.textContent += `[${timestamp}] ${message}\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function testStudentData() {
            log('=== 測試學生資料 ===');
            log(`學生總數: ${students.length}`);
            log(`前5位學生: ${students.slice(0, 5).map(s => `${s.id}號 ${s.name}`).join(', ')}`);
            log('學生資料測試完成');
        }
        
        function testSelectors() {
            log('=== 測試選擇器 ===');
            
            const select1 = document.getElementById('testSelect1');
            const select2 = document.getElementById('testSelect2');
            
            if (!select1 || !select2) {
                log('錯誤: 找不到選擇器元素');
                return;
            }
            
            // 清空選項
            select1.innerHTML = '<option value="">🎓 選擇學生</option>';
            select2.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
            
            // 添加學生選項
            students.forEach(student => {
                const option1 = document.createElement('option');
                option1.value = student.id;
                option1.textContent = `${student.id}號 ${student.name}`;
                select1.appendChild(option1);
                
                const option2 = document.createElement('option');
                option2.value = student.id;
                option2.textContent = `${student.id}號 ${student.name}（${student.fullName}）`;
                select2.appendChild(option2);
            });
            
            log(`選擇器1選項數量: ${select1.options.length}`);
            log(`選擇器2選項數量: ${select2.options.length}`);
            log('選擇器測試完成');
        }
        
        function clearDebug() {
            document.getElementById('debugInfo').textContent = '';
        }
        
        // 頁面載入時自動測試
        window.addEventListener('DOMContentLoaded', function() {
            log('頁面載入完成，開始自動測試...');
            testStudentData();
            testSelectors();
        });
    </script>
</body>
</html>

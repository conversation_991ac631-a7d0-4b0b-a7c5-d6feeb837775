<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化測試 - 學生選擇器</title>
    <style>
        body {
            font-family: 'Noto Sans TC', sans-serif;
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #ff6b9d;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .debug {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        
        .stats {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-weight: bold;
            color: #ff6b9d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 學生選擇器測試</h1>
        
        <div class="stats">
            <div class="stat-item">
                <span>班級目標</span>
                <span class="stat-value" id="goalTarget">270</span>
            </div>
            <div class="stat-item">
                <span>平均每人</span>
                <span class="stat-value" id="avgPerStudent">10</span>
            </div>
        </div>
        
        <div class="form-group">
            <label for="studentSelect">頂部學生選擇器</label>
            <select id="studentSelect">
                <option value="">選擇學生</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="studentId">表單學生選擇器</label>
            <select id="studentId">
                <option value="">請選擇學生</option>
            </select>
        </div>
        
        <div class="debug" id="debugOutput">載入中...</div>
    </div>

    <script>
        // 複製主要邏輯
        let books = [];
        let userSettings = { yearlyGoal: 270 };
        
        const defaultStudents = [
            { id: '01', name: '孔子', fullName: '孔丘' },
            { id: '02', name: '老子', fullName: '李耳' },
            { id: '03', name: '孟子', fullName: '孟軻' },
            { id: '04', name: '莊子', fullName: '莊周' },
            { id: '05', name: '荀子', fullName: '荀況' },
            { id: '06', name: '墨子', fullName: '墨翟' },
            { id: '07', name: '韓非子', fullName: '韓非' },
            { id: '08', name: '司馬遷', fullName: '司馬遷' },
            { id: '09', name: '諸葛亮', fullName: '諸葛亮' },
            { id: '10', name: '李白', fullName: '李太白' }
        ];
        
        let students = JSON.parse(localStorage.getItem('classReadingTracker_students')) || [...defaultStudents];
        let elements = {};
        
        function log(message) {
            const debugOutput = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.textContent += `[${timestamp}] ${message}\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        function initializeElements() {
            log('初始化 DOM 元素...');
            
            elements = {
                studentSelect: document.getElementById('studentSelect'),
                studentId: document.getElementById('studentId'),
                goalTarget: document.getElementById('goalTarget'),
                avgPerStudent: document.getElementById('avgPerStudent')
            };
            
            log('studentSelect: ' + (elements.studentSelect ? '✅' : '❌'));
            log('studentId: ' + (elements.studentId ? '✅' : '❌'));
            log('goalTarget: ' + (elements.goalTarget ? '✅' : '❌'));
            log('avgPerStudent: ' + (elements.avgPerStudent ? '✅' : '❌'));
            
            log('DOM 元素初始化完成');
        }
        
        function updateStudentSelectors() {
            log('正在更新學生選擇器...');
            log('學生數量: ' + students.length);
            
            if (!elements.studentSelect || !elements.studentId) {
                log('❌ 找不到必要的選擇器元素');
                return;
            }
            
            // 清空現有選項
            elements.studentSelect.innerHTML = '<option value="">🎓 選擇學生</option>';
            elements.studentId.innerHTML = '<option value="">👨‍🎓 請選擇學生</option>';
            
            // 添加學生選項
            students.forEach(student => {
                const option1 = document.createElement('option');
                option1.value = student.id;
                option1.textContent = `${student.id}號 ${student.name}`;
                elements.studentSelect.appendChild(option1);
                
                const option2 = document.createElement('option');
                option2.value = student.id;
                option2.textContent = `${student.id}號 ${student.name}（${student.fullName}）`;
                elements.studentId.appendChild(option2);
            });
            
            log('✅ 學生選擇器更新完成');
            log('studentSelect 選項數: ' + elements.studentSelect.options.length);
            log('studentId 選項數: ' + elements.studentId.options.length);
        }
        
        function initializeStudentSelectors() {
            updateStudentSelectors();
        }
        
        function initializeApp() {
            log('開始初始化應用...');
            log('學生數據: ' + students.length + ' 位學生');
            
            // 初始化學生選單
            initializeStudentSelectors();
            
            // 設定目標顯示
            if (elements.goalTarget) {
                elements.goalTarget.textContent = userSettings.yearlyGoal;
                log('設定年度目標: ' + userSettings.yearlyGoal);
            }
            
            if (elements.avgPerStudent) {
                elements.avgPerStudent.textContent = Math.round(userSettings.yearlyGoal / 27);
                log('設定平均每人目標: ' + Math.round(userSettings.yearlyGoal / 27));
            }
            
            log('✅ 應用初始化完成');
        }
        
        // 初始化流程
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM 載入完成，開始初始化...');
            
            setTimeout(() => {
                initializeElements();
                initializeApp();
            }, 100);
        });
    </script>
</body>
</html>
